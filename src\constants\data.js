import images from './images';

// Regular menu items (original data)
const wines = [
  {
    title: 'Chapel Hill Shiraz',
    price: '$56',
    tags: 'AU | Bottle',
  },
  {
    title: '<PERSON><PERSON>bee',
    price: '$59',
    tags: 'AU | Bottle',
  },
  {
    title: 'La Vieillw Rose',
    price: '$44',
    tags: 'FR | 750 ml',
  },
  {
    title: 'Rhino Pale Ale',
    price: '$31',
    tags: 'CA | 750 ml',
  },
  {
    title: 'Irish Guinness',
    price: '$26',
    tags: 'IE | 750 ml',
  },
];

const cocktails = [
  {
    title: 'Aperol Sprtiz',
    price: '$20',
    tags: 'Aperol | Villa Marchesi prosecco | soda | 30 ml',
  },
  {
    title: "Dark 'N' Stormy",
    price: '$16',
    tags: 'Dark rum | Ginger beer | Slice of lime',
  },
  {
    title: '<PERSON><PERSON><PERSON>',
    price: '$10',
    tags: 'Rum | Citrus juice | Sugar',
  },
  {
    title: 'Old Fashioned',
    price: '$31',
    tags: 'Bourbon | Brown sugar | Angostura Bitters',
  },
  {
    title: 'Negroni',
    price: '$26',
    tags: 'Gin | Sweet Vermouth | Campari | Orange garnish',
  },
];

const awards = [
  {
    imgUrl: images.award02,
    title: 'Bib Gourmond',
    subtitle: 'Lorem ipsum dolor sit amet, consectetur.',
  },
  {
    imgUrl: images.award01,
    title: 'Rising Star',
    subtitle: 'Lorem ipsum dolor sit amet, consectetur.',
  },
  {
    imgUrl: images.award05,
    title: 'AA Hospitality',
    subtitle: 'Lorem ipsum dolor sit amet, consectetur.',
  },
  {
    imgUrl: images.award03,
    title: 'Outstanding Chef',
    subtitle: 'Lorem ipsum dolor sit amet, consectetur.',
  },
];

// Full menu data (for the dedicated menu page)
const fullMenu = {
  wines: [
    ...wines,
    {
      title: 'Chateau Margaux',
      price: '$120',
      tags: 'FR | Bottle',
    },
    {
      title: 'Dom Pérignon',
      price: '$200',
      tags: 'FR | Bottle',
    },
    {
      title: 'Cloudy Bay Sauvignon Blanc',
      price: '$65',
      tags: 'NZ | Bottle',
    }
  ],
  cocktails: [
    ...cocktails,
    {
      title: 'Espresso Martini',
      price: '$18',
      tags: 'Vodka | Kahlúa | Espresso',
    },
    {
      title: 'Moscow Mule',
      price: '$15',
      tags: 'Vodka | Ginger beer | Lime juice | Mint',
    },
    {
      title: 'Margarita',
      price: '$14',
      tags: 'Tequila | Triple sec | Lime juice | Salt rim',
    }
  ],
  mainCourses: [
    {
      title: 'Filet Mignon',
      price: '$48',
      tags: 'Prime beef | Truffle butter | Seasonal vegetables',
    },
    {
      title: 'Lobster Thermidor',
      price: '$52',
      tags: 'Maine lobster | Brandy cream sauce | Gruyère cheese',
    },
    {
      title: 'Rack of Lamb',
      price: '$45',
      tags: 'Herb crusted | Mint jus | Roasted potatoes',
    },
    {
      title: 'Duck a l\'Orange',
      price: '$38',
      tags: 'Roasted duck breast | Orange sauce | Wild rice',
    },
    {
      title: 'Seafood Risotto',
      price: '$36',
      tags: 'Arborio rice | Shrimp | Scallops | Saffron',
    }
  ],
  desserts: [
    {
      title: 'Crème Brûlée',
      price: '$14',
      tags: 'Vanilla bean | Caramelized sugar',
    },
    {
      title: 'Chocolate Soufflé',
      price: '$16',
      tags: 'Grand Marnier | Vanilla ice cream',
    },
    {
      title: 'Tiramisu',
      price: '$12',
      tags: 'Mascarpone | Espresso | Cocoa',
    },
    {
      title: 'Lemon Tart',
      price: '$13',
      tags: 'Meyer lemon curd | Toasted meringue',
    },
    {
      title: 'Cheese Plate',
      price: '$22',
      tags: 'Selection of artisanal cheeses | Honeycomb | Crackers',
    }
  ]
};

const data = { wines, cocktails, awards, fullMenu };

export default data;
