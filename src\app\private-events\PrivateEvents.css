.app__privateevents {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #0A1622;
  position: relative;
  padding: 0;
  color: #fff;
}

.app__privateevents .app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-navy);
}

.app__privateevents-heading {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  border-bottom: 1px solid rgba(220, 202, 135, 0.18);
  padding-bottom: 1.5rem;
}

.app__privateevents-heading h1 {
  text-shadow: 0 3px 10px rgba(0, 29, 63, 0.5);
}

.app__privateevents-back-link {
  margin-top: 1rem;
}

.app__privateevents-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__privateevents-back-link a:hover {
  color: var(--color-white);
}

/* Main container layout */
.app__privateevents-container {
  display: flex;
  flex-direction: column;
  gap: 6rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero section with full-width image */
.app__privateevents-hero {
  position: relative;
  width: 100%;
  height: 90vh;
  margin-bottom: 5rem;
  overflow: hidden;
}

.app__privateevents-hero_image {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.app__privateevents-hero_image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.7;
}

.app__privateevents-hero_overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(0deg, rgba(10, 22, 34, 1) 0%, rgba(10, 22, 34, 0.7) 50%, rgba(10, 22, 34, 0.4) 100%);
  z-index: 2;
}

.app__privateevents-hero_content {
  position: absolute;
  bottom: 5rem;
  left: 5rem;
  z-index: 3;
  max-width: 650px;
}

.app__privateevents-hero_title {
  font-family: var(--font-base);
  font-size: 3.5rem;
  color: #fff;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  letter-spacing: -1px;
}

.app__privateevents-hero_description {
  font-family: var(--font-alt);
  font-size: 1.1rem;
  line-height: 1.8;
  color: #f1f1f1;
  max-width: 550px;
}

.app__privateevents-hero_cta {
  margin-top: 2rem;
}

.app__privateevents-hero_cta button {
  background: transparent;
  border: 1px solid #E8D58E;
  color: #E8D58E;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
}

.app__privateevents-hero_cta button:hover {
  background: #E8D58E;
  color: #0A1622;
}

/* Venue section */
.app__privateevents-section {
  padding: 4rem 0;
}

.app__privateevents-section_heading {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 4rem;
}

.section-title {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 2.5rem;
  line-height: 1.2;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -15px;
  width: 60px;
  height: 2px;
  background: #E8D58E;
}

.app__privateevents-venues_grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.venue-card {
  position: relative;
  height: 450px;
  overflow: hidden;
  cursor: pointer;
}

.venue-card_image {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.venue-card_image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 1s ease;
}

.venue-card:hover .venue-card_image img {
  transform: scale(1.05);
}

.venue-card_overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(0deg, rgba(10, 22, 34, 0.85) 0%, rgba(10, 22, 34, 0.6) 50%, rgba(10, 22, 34, 0.3) 100%);
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.venue-card:hover .venue-card_overlay {
  opacity: 1;
}

.venue-card_content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  z-index: 3;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.venue-card:hover .venue-card_content {
  transform: translateY(0);
  opacity: 1;
}

.venue-card_content h3 {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.venue-card_content p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.venue-card_capacity {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 0.95rem;
  margin-bottom: 1rem;
}

.venue-card_details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.venue-card_detail {
  background: rgba(232, 213, 142, 0.1);
  border: 1px solid rgba(232, 213, 142, 0.3);
  padding: 0.3rem 0.7rem;
  font-size: 0.75rem;
  color: #E8D58E;
}

/* Contact form section */
.app__privateevents-contact {
  padding: 5rem 0;
  background: rgba(13, 28, 43, 0.5);
  margin: 5rem 0;
}

.app__privateevents-contact_inner {
  max-width: 800px;
  margin: 0 auto;
  padding: 3rem;
  background: rgba(10, 22, 34, 0.8);
  border: 1px solid rgba(232, 213, 142, 0.1);
}

.app__privateevents-contact_heading {
  text-align: center;
  margin-bottom: 3rem;
}

.app__privateevents-contact h2 {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.app__privateevents-contact p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.8;
}

/* Features section */
.app__privateevents-contact_features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.contact-feature {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(232, 213, 142, 0.1);
  transition: all 0.3s ease;
}

.contact-feature:hover {
  background: rgba(232, 213, 142, 0.05);
  border-color: rgba(232, 213, 142, 0.2);
  transform: translateY(-5px);
}

.contact-feature_icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.contact-feature h3 {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
}

.contact-feature p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
}

/* CTA section */
.app__privateevents-contact_cta {
  text-align: center;
  margin-top: 3rem;
}

.contact-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: transparent;
  border: 2px solid #E8D58E;
  color: #E8D58E;
  padding: 1.2rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-cta-button:hover {
  background: #E8D58E;
  color: #0A1622;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(232, 213, 142, 0.3);
}

.button-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.contact-cta-button:hover .button-arrow {
  transform: translateX(5px);
}

.contact-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(232, 213, 142, 0.2);
}

.contact-info p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

.contact-info strong {
  color: #E8D58E;
}

/* Responsive styles */
@media screen and (max-width: 1150px) {
  .app__privateevents-hero_content {
    left: 3rem;
    bottom: 3rem;
  }
  
  .app__privateevents-hero_title {
    font-size: 3rem;
  }
}

@media screen and (max-width: 850px) {
  .app__privateevents-hero {
    height: 70vh;
  }
  
  .app__privateevents-hero_content {
    left: 2rem;
    bottom: 2rem;
    max-width: 500px;
  }
  
  .app__privateevents-hero_title {
    font-size: 2.5rem;
  }
  
  .app__privateevents-venues_grid {
    grid-template-columns: 1fr;
  }

  .app__privateevents-contact_features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-cta-button {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .app__privateevents-contact_inner {
    padding: 2rem 1.5rem;
  }
}

@media screen and (max-width: 576px) {
  .app__privateevents-hero {
    height: 60vh;
  }
  
  .app__privateevents-hero_content {
    left: 1.5rem;
    bottom: 1.5rem;
  }
  
  .app__privateevents-hero_title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .app__privateevents-container {
    padding: 0 1rem;
  }
  
  .app__privateevents-contact {
    margin: 3rem -1rem;
    padding: 3rem 0;
  }
  
  .app__privateevents-contact_inner {
    max-width: 100%;
    width: 100%;
    padding: 2rem 1rem;
    border-left: none;
    border-right: none;
  }

  .contact-feature {
    padding: 1.5rem 1rem;
  }

  .contact-feature_icon {
    font-size: 2rem;
  }

  .contact-cta-button {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    gap: 0.5rem;
  }

  .contact-info p {
    font-size: 0.85rem;
  }
} 