.app__privateevents {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #0A1622;
  position: relative;
  padding: 0;
  color: #fff;
}

.app__privateevents .app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-navy);
}

.app__privateevents-heading {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  border-bottom: 1px solid rgba(220, 202, 135, 0.18);
  padding-bottom: 1.5rem;
}

.app__privateevents-heading h1 {
  text-shadow: 0 3px 10px rgba(0, 29, 63, 0.5);
}

.app__privateevents-back-link {
  margin-top: 1rem;
}

.app__privateevents-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__privateevents-back-link a:hover {
  color: var(--color-white);
}

/* Main container layout */
.app__privateevents-container {
  display: flex;
  flex-direction: column;
  gap: 6rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero section with full-width image */
.app__privateevents-hero {
  position: relative;
  width: 100%;
  height: 90vh;
  margin-bottom: 5rem;
  overflow: hidden;
}

.app__privateevents-hero_image {
  position: absolute;
  inset: 0;
  z-index: 1;
}

.app__privateevents-hero_image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.7;
}

.app__privateevents-hero_overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(0deg, rgba(10, 22, 34, 1) 0%, rgba(10, 22, 34, 0.7) 50%, rgba(10, 22, 34, 0.4) 100%);
  z-index: 2;
}

.app__privateevents-hero_content {
  position: absolute;
  bottom: 5rem;
  left: 5rem;
  z-index: 3;
  max-width: 650px;
}

.app__privateevents-hero_title {
  font-family: var(--font-base);
  font-size: 3.5rem;
  color: #fff;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  letter-spacing: -1px;
}

.app__privateevents-hero_description {
  font-family: var(--font-alt);
  font-size: 1.1rem;
  line-height: 1.8;
  color: #f1f1f1;
  max-width: 550px;
}

.app__privateevents-hero_cta {
  margin-top: 2rem;
}

.app__privateevents-hero_cta button {
  background: transparent;
  border: 1px solid #E8D58E;
  color: #E8D58E;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
}

.app__privateevents-hero_cta button:hover {
  background: #E8D58E;
  color: #0A1622;
}

/* Venue showcase section */
.app__privateevents-venues {
  padding: 6rem 0;
  background: #0A1622;
}

.venues-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.venue-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 8rem;
  align-items: center;
  min-height: 600px;
}

.venue-showcase:last-child {
  margin-bottom: 0;
}

.venue-showcase--reverse {
  direction: rtl;
}

.venue-showcase--reverse > * {
  direction: ltr;
}

.venue-showcase_image {
  position: relative;
  height: 600px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.venue-showcase_image img {
  transition: transform 0.8s ease;
}

.venue-showcase:hover .venue-showcase_image img {
  transform: scale(1.05);
}

.venue-showcase_image-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(10, 22, 34, 0.3) 0%, rgba(10, 22, 34, 0.1) 100%);
  z-index: 2;
}

.venue-showcase_content {
  padding: 3rem;
  display: flex;
  align-items: center;
  min-height: 600px;
}

.venue-showcase_text {
  width: 100%;
}

.venue-subtitle {
  font-family: var(--font-alt);
  color: #E8D58E;
  font-size: 0.9rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  margin-bottom: 1rem;
  font-weight: 300;
}

.venue-title {
  font-family: var(--font-base);
  color: #fff;
  font-size: 3.2rem;
  line-height: 1.1;
  margin-bottom: 2rem;
  font-weight: 400;
  letter-spacing: -1px;
}

.venue-description {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 3rem;
  max-width: 500px;
}

.venue-details {
  margin-bottom: 3rem;
}

.venue-capacity {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.capacity-label {
  font-family: var(--font-alt);
  color: #E8D58E;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.capacity-value {
  font-family: var(--font-base);
  color: #fff;
  font-size: 1.1rem;
}

.venue-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.venue-feature {
  background: rgba(232, 213, 142, 0.1);
  border: 1px solid rgba(232, 213, 142, 0.2);
  padding: 0.5rem 1rem;
  font-family: var(--font-alt);
  font-size: 0.8rem;
  color: #E8D58E;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.venue-cta {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: transparent;
  border: 2px solid #E8D58E;
  color: #E8D58E;
  padding: 1rem 2.5rem;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.venue-cta:hover {
  background: #E8D58E;
  color: #0A1622;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(232, 213, 142, 0.3);
}

.cta-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.venue-cta:hover .cta-arrow {
  transform: translateX(5px);
}

/* Contact form section */
.app__privateevents-contact {
  padding: 5rem 0;
  background: rgba(13, 28, 43, 0.5);
  margin: 5rem 0;
}

.app__privateevents-contact_inner {
  max-width: 800px;
  margin: 0 auto;
  padding: 3rem;
  background: rgba(10, 22, 34, 0.8);
  border: 1px solid rgba(232, 213, 142, 0.1);
}

.app__privateevents-contact_heading {
  text-align: center;
  margin-bottom: 3rem;
}

.app__privateevents-contact h2 {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.app__privateevents-contact p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.8;
}

/* Features section */
.app__privateevents-contact_features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.contact-feature {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(232, 213, 142, 0.1);
  transition: all 0.3s ease;
}

.contact-feature:hover {
  background: rgba(232, 213, 142, 0.05);
  border-color: rgba(232, 213, 142, 0.2);
  transform: translateY(-5px);
}

.contact-feature_icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.contact-feature h3 {
  font-family: var(--font-base);
  color: #E8D58E;
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
}

.contact-feature p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0;
}

/* CTA section */
.app__privateevents-contact_cta {
  text-align: center;
  margin-top: 3rem;
}

.contact-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: transparent;
  border: 2px solid #E8D58E;
  color: #E8D58E;
  padding: 1.2rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-cta-button:hover {
  background: #E8D58E;
  color: #0A1622;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(232, 213, 142, 0.3);
}

.button-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.contact-cta-button:hover .button-arrow {
  transform: translateX(5px);
}

.contact-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(232, 213, 142, 0.2);
}

.contact-info p {
  font-family: var(--font-alt);
  color: #f1f1f1;
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

.contact-info strong {
  color: #E8D58E;
}

/* Responsive styles */
@media screen and (max-width: 1150px) {
  .app__privateevents-hero_content {
    left: 3rem;
    bottom: 3rem;
  }
  
  .app__privateevents-hero_title {
    font-size: 3rem;
  }
}

@media screen and (max-width: 850px) {
  .app__privateevents-hero {
    height: 70vh;
  }
  
  .app__privateevents-hero_content {
    left: 2rem;
    bottom: 2rem;
    max-width: 500px;
  }
  
  .app__privateevents-hero_title {
    font-size: 2.5rem;
  }
  
  .venue-showcase {
    grid-template-columns: 1fr;
    gap: 3rem;
    margin-bottom: 6rem;
  }

  .venue-showcase--reverse {
    direction: ltr;
  }

  .venue-showcase_image {
    height: 400px;
  }

  .venue-showcase_content {
    padding: 2rem;
    min-height: auto;
  }

  .venue-title {
    font-size: 2.5rem;
  }

  .app__privateevents-contact_features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-cta-button {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .app__privateevents-contact_inner {
    padding: 2rem 1.5rem;
  }
}

@media screen and (max-width: 576px) {
  .app__privateevents-hero {
    height: 60vh;
  }
  
  .app__privateevents-hero_content {
    left: 1.5rem;
    bottom: 1.5rem;
  }
  
  .app__privateevents-hero_title {
    font-size: 2rem;
  }
  
  .venues-container {
    padding: 0 1rem;
  }

  .venue-showcase {
    gap: 2rem;
    margin-bottom: 4rem;
  }

  .venue-showcase_image {
    height: 300px;
  }

  .venue-showcase_content {
    padding: 1.5rem;
  }

  .venue-title {
    font-size: 2rem;
  }

  .venue-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .venue-cta {
    padding: 0.8rem 2rem;
    font-size: 0.8rem;
  }

  .app__privateevents-container {
    padding: 0 1rem;
  }
  
  .app__privateevents-contact {
    margin: 3rem -1rem;
    padding: 3rem 0;
  }
  
  .app__privateevents-contact_inner {
    max-width: 100%;
    width: 100%;
    padding: 2rem 1rem;
    border-left: none;
    border-right: none;
  }

  .contact-feature {
    padding: 1.5rem 1rem;
  }

  .contact-feature_icon {
    font-size: 2rem;
  }

  .contact-cta-button {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    gap: 0.5rem;
  }

  .contact-info p {
    font-size: 0.85rem;
  }
} 