.app__chef-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 2rem;
  margin-bottom: 3rem;
}

.menu-button-container {
  margin-top: 2rem;
  display: flex;
  justify-content: flex-start;
}

.app__chef-content_quote {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}

.app__chef-content_quote img {
  width: 47px;
  height: 40px;
  margin: 0 1rem 1rem 0;
}

.app__dishes-carousel {
  width: 100%;
  position: relative;
  overflow: hidden;
  margin-top: 2rem;
  padding-bottom: 3rem;
}

.app__dishes-slider {
  display: flex;
  transition: transform 0.5s ease;
  width: 100%;
  height: 100%;
}

.dish-slide {
  min-width: 100%;
  display: flex;
  justify-content: center;
}

.dish-item {
  position: relative;
  width: 90%;
  max-width: 600px;
  text-align: center;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dish-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.dish-img-container {
  position: relative;
  width: 100%;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.dish-img {
  object-fit: cover;
  border-radius: 8px;
}

.dish-title {
  font-family: var(--font-base);
  color: var(--color-golden);
  font-size: 24px;
  margin-bottom: 1rem;
}

.dish-desc {
  font-family: var(--font-alt);
  color: var(--color-white);
  font-size: 16px;
  line-height: 1.6;
  flex-grow: 1;
}

.app__dishes-arrows {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  padding: 0 1rem;
}

.dishes__arrow-icon {
  color: var(--color-golden);
  font-size: 2.5rem;
  cursor: pointer;
  background: var(--color-black);
  border-radius: 50%;
  padding: 0.5rem;
  opacity: 0.8;
  transition: all 0.3s ease;
  pointer-events: auto;
  z-index: 10;
}

.dishes__arrow-icon:hover {
  color: var(--color-white);
  opacity: 1;
  transform: scale(1.1);
}

.dishes__arrow-icon.left {
  margin-left: 1rem;
}

.dishes__arrow-icon.right {
  margin-right: 1rem;
}

.app__dishes-dots {
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 10px;
  width: 100%;
}

.dot {
  height: 10px;
  width: 10px;
  margin: 0 5px;
  background-color: var(--color-grey);
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background-color: var(--color-golden);
  transform: scale(1.2);
}

.app__inspiration {
  width: 100%;
  margin-top: 4rem;
  border-top: 1px solid rgba(220, 202, 135, 0.2);
  padding-top: 2rem;
}

.app__inspiration p {
  margin: 2rem 0;
}

.app__chef-sign {
  width: 100%;
  margin-top: 3rem;
}

.app__chef-sign p:first-child {
  font-family: var(--font-base);
  font-weight: 400;
  font-size: 32px;
  line-height: 41.6px;
  letter-spacing: 0.04em;
  text-transform: capitalize;
  color: var(--color-golden);
}

.app__chef-sign img {
  width: 250px;
  margin-top: 3rem;
}

@media screen and (min-width: 2000px) {
  .app__chef-sign img {
    width: 370px;
  }
  
  .app__chef-content_quote img {
    width: 60px;
    height: 50px;
  }
  
  .app__inspiration {
    margin-top: 6rem;
    padding-top: 3rem;
  }

  .dish-img-container {
    height: 400px;
  }
  
  .dish-title {
    font-size: 32px;
  }
  
  .dish-desc {
    font-size: 20px;
  }

  .dishes__arrow-icon {
    font-size: 3.5rem;
  }
}

@media screen and (max-width: 850px) {
  .dish-item {
    width: 95%;
  }
  
  .dish-img-container {
    height: 250px;
  }
}

@media screen and (max-width: 650px) {
  .app__chef-sign img {
    width: 80%;
  }
  
  .app__chef-content_quote {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .menu-button-container {
    justify-content: center;
    margin-top: 1.5rem;
  }
  
  .app__inspiration {
    margin-top: 3rem;
    padding-top: 1.5rem;
  }
  
  .dish-img-container {
    height: 200px;
  }
  
  .dish-title {
    font-size: 20px;
    margin-bottom: 0.5rem;
  }
  
  .dish-desc {
    font-size: 14px;
    line-height: 1.4;
  }

  .dishes__arrow-icon {
    font-size: 1.8rem;
    padding: 0.3rem;
  }
  
  .app__dishes-dots {
    bottom: 5px;
  }
  
  .dot {
    height: 8px;
    width: 8px;
    margin: 0 3px;
  }
}

