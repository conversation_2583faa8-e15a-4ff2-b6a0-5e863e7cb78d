.app__specialMenu {
  flex-direction: column;
  background: var(--color-black);
  position: relative;
}

.app__specialMenu-title {
  margin-bottom: 2rem;
  text-align: center;
  position: relative;
}

.app__specialMenu-title h1 {
  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.app__specialMenu-title h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-golden), transparent);
}

.app__specialMenu-title_description {
  max-width: 700px;
  margin: 1.5rem auto 0;
  color: var(--color-grey);
  line-height: 1.6;
}

.app__specialMenu-menu {
  width: 100%;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
}

/* Heading styles */
.app__specialMenu-menu_heading-container {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.app__specialMenu-menu_heading {
  font-family: var(--font-base);
  font-weight: 600;
  font-size: 45px;
  line-height: 58.5px;
  letter-spacing: 0.04em;
  color: var(--color-white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.app__specialMenu-menu_heading::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -15px;
  width: 25px;
  height: 25px;
  background: radial-gradient(circle, rgba(12, 28, 53, 0.2) 0%, transparent 70%);
  z-index: -1;
}

.app__specialMenu-menu_heading-line {
  width: 80px;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--color-golden), transparent);
  margin: 0.5rem auto;
  box-shadow: 0 1px 3px rgba(220, 202, 135, 0.3);
}

/* Wine and Cocktails sections */
.app__specialMenu-menu_wine,
.app__specialMenu-menu_cocktails {
  flex: 1;
  width: 100%;
  flex-direction: column;
  padding: 0 1.5rem;
  transition: all 0.4s ease-in-out;
  position: relative;
}

.app__specialMenu-menu_wine::before,
.app__specialMenu-menu_cocktails::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(12, 28, 53, 0.05) 0%, rgba(12, 28, 53, 0.15) 100%);
  border-radius: 5px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.app__specialMenu-menu_wine:hover::before,
.app__specialMenu-menu_cocktails:hover::before {
  opacity: 1;
}

.app__specialMenu-menu_wine:hover,
.app__specialMenu-menu_cocktails:hover {
  transform: translateY(-5px);
}

/* Image styling */
.app__specialMenu-menu_img {
  width: 280px;
  margin: 0 2rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.app__specialMenu-menu_img::before {
  content: '';
  position: absolute;
  top: -15px;
  right: -15px;
  width: 0;
  height: 0;
  z-index: -1;
}

.app__specialMenu-menu_img::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: -15px;
  width: 0;
  height: 0;
  z-index: -1;
}

.app__specialMenu-menu_img-inner {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: contain;
  border-radius: 5px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  transition: all 0.4s ease-in-out;
  opacity: 0.9;
  border: 1px solid rgba(220, 202, 135, 0.3);
  background-color: transparent;
}

.app__specialMenu-menu_img:hover .app__specialMenu-menu_img-inner {
  transform: scale(1.03);
  opacity: 1;
  filter: brightness(0.8);
  border: 1px solid rgba(220, 202, 135, 0.6);
}

/* Overlay for menu image */
.app__specialMenu-menu_img-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  background: linear-gradient(to bottom, transparent, rgba(12, 28, 53, 0.5));
  border-radius: 5px;
}

.app__specialMenu-menu_img:hover .app__specialMenu-menu_img-overlay {
  opacity: 1;
  pointer-events: all;
}

.floating-button {
  transform: translateY(20px);
  transition: transform 0.4s ease, background-color 0.3s ease, color 0.3s ease;
  padding: 1rem 2rem;
  font-size: 18px;
  background-color: rgba(220, 202, 135, 0.9);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(220, 202, 135, 0.2);
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.floating-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.4s ease;
}

.app__specialMenu-menu_img:hover .floating-button {
  transform: translateY(0);
}

.floating-button:hover {
  background-color: var(--color-golden);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(220, 202, 135, 0.8);
}

.floating-button:hover::before {
  left: 100%;
}

/* Menu items */
.app__specialMenu_menu_items {
  display: flex;
  flex-direction: column;
  margin: 2rem 0 1rem;
  width: 100%;
}

/* Chef note */
.app__specialMenu-chef-note {
  margin-top: 0;
  text-align: center;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 1.5rem 1.5rem;
  position: relative;
}

.app__specialMenu-chef-note::before {
  display: none;
}

.app__specialMenu-chef-note::after {
  display: none;
}

.app__specialMenu-chef-note_icon {
  margin-bottom: 1.5rem;
  position: relative;
}

.app__specialMenu-chef-note_icon::after {
  display: none;
}

.app__specialMenu-chef-note p:first-of-type {
  font-style: italic;
  line-height: 1.8;
  margin-bottom: 1rem;
  color: var(--color-grey);
  position: relative;
  z-index: 1;
}

.app__specialMenu-chef-note p:last-of-type {
  color: var(--color-golden);
  font-size: 18px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.app__specialMenu-chef-note p:last-of-type::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-golden), transparent);
}

/* Responsive styles */
@media screen and (min-width: 2000px) {
  .app__specialMenu-menu_img {
    width: 350px;
  }

  .app__specialMenu-menu_img img {
    max-height: 650px;
  }
}

@media screen and (max-width: 1150px) {
  .app__specialMenu-menu {
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .app__specialMenu-menu_img {
    margin: 3rem 0;
  }
  
  .app__specialMenu-menu_wine,
  .app__specialMenu-menu_cocktails {
    padding: 0;
    max-width: 650px;
    width: 100%;
  }
  
  .app__specialMenu-menu_img-overlay {
    opacity: 1;
    pointer-events: all;
  }
  
  .floating-button {
    transform: translateY(0);
  }
}

@media screen and (max-width: 650px) {
  .app__specialMenu-menu_img {
    width: 100%;
    max-width: 240px;
  }

  .app__specialMenu-menu_img-inner {
    max-height: 450px;
  }

  .app__specialMenu-menu_heading {
    font-size: 35px;
    line-height: 48.5px;
  }
  
  .app__specialMenu-chef-note {
    padding: 1.5rem;
  }
  
  .app__specialMenu-chef-note::before {
    font-size: 100px;
    left: 10px;
  }
}
