/* Main contact page layout */
.app__contactpage {
  min-height: 100vh;
  background: var(--color-black);
  color: #fff;
  padding: 0;
}

/* Header Section */
.contact-header {
  padding: 4rem 2rem 2rem;
  text-align: left;
  max-width: 1200px;
  margin: 0 auto;
}

.contact-main-title {
  font-family: var(--font-base);
  font-size: 4rem;
  font-weight: 400;
  color: #fff;
  margin-bottom: 2rem;
  line-height: 1.1;
}

.contact-main-description {
  font-family: var(--font-alt);
  font-size: 1.1rem;
  line-height: 1.6;
  color: #ccc;
  max-width: 600px;
}

/* Main Content Layout */
.contact-main-content {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  align-items: start;
}

/* Contact Form Section */
.contact-form-section {
  max-width: 350px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #fff;
  margin-bottom: 0.5rem;
  font-weight: 400;
}

.form-group input,
.form-group textarea {
  background: transparent;
  border: none;
  border-bottom: 1px solid #555;
  color: #fff;
  padding: 0.8rem 0;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-bottom-color: var(--color-golden);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666;
  font-size: 0.85rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.submit-btn {
  background: var(--color-golden);
  color: var(--color-black);
  border: none;
  padding: 0.8rem 2rem;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  background: transparent;
  color: var(--color-golden);
  border: 1px solid var(--color-golden);
}

.form-success {
  background: rgba(20, 50, 20, 0.5);
  border: 1px solid rgba(50, 220, 50, 0.3);
  padding: 1.5rem;
  border-radius: 4px;
  text-align: center;
}

/* Working Hours Section */
.working-hours-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.working-hours-card {
  background: var(--color-golden);
  padding: 2.5rem 2rem;
  border-radius: 4px;
  text-align: center;
  min-width: 280px;
}

.working-hours-card h2 {
  font-family: var(--font-base);
  font-size: 1.8rem;
  color: var(--color-black);
  margin-bottom: 2rem;
  font-weight: 400;
}

.hours-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
}

.hours-item:last-child {
  margin-bottom: 0;
}

.day {
  font-family: var(--font-alt);
  font-size: 1rem;
  color: var(--color-black);
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.time {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: var(--color-black);
  opacity: 0.8;
}

/* Contact Information Section */
.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 350px;
}

.contact-info-item {
  display: flex;
  flex-direction: column;
}

.contact-info-item h3 {
  font-family: var(--font-alt);
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0.8rem;
  font-weight: 500;
}

.contact-info-item p {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 0.3rem;
  line-height: 1.4;
}

.contact-info-item p:last-child {
  margin-bottom: 0;
}

/* Google Maps Section */
.contact-map-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--color-black) 0%, rgba(15, 40, 70, 0.95) 100%);
  position: relative;
}

.contact-map-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-golden), transparent);
}

.contact-map-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.map-header {
  text-align: center;
  margin-bottom: 3rem;
}

.map-subtitle {
  font-family: var(--font-alt);
  color: var(--color-golden);
  font-size: 0.9rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  margin-bottom: 1rem;
  font-weight: 300;
}

.map-title {
  font-family: var(--font-base);
  color: #fff;
  font-size: 3.2rem;
  line-height: 1.1;
  margin-bottom: 2rem;
  font-weight: 400;
  letter-spacing: -1px;
}

.map-description {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 1.1rem;
  line-height: 1.8;
  max-width: 600px;
  margin: 0 auto;
}

.map-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Decorative Golden Map Frame */
.map-frame {
  position: relative;
  height: 500px;
  padding: 1rem;
}

.map-golden-border {
  position: relative;
  height: 100%;
  border: 3px solid var(--color-golden);
  border-radius: 8px;
  padding: 1rem;
  background: linear-gradient(45deg,
    rgba(220, 202, 135, 0.1) 0%,
    rgba(220, 202, 135, 0.05) 50%,
    rgba(220, 202, 135, 0.1) 100%);
  box-shadow:
    0 0 20px rgba(220, 202, 135, 0.3),
    inset 0 0 20px rgba(220, 202, 135, 0.1);
}

.map-inner-frame {
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(220, 202, 135, 0.3);
}

.map-inner-frame iframe {
  filter: sepia(10%) saturate(90%) hue-rotate(15deg);
  transition: filter 0.3s ease;
}

.map-frame:hover .map-inner-frame iframe {
  filter: sepia(0%) saturate(100%) hue-rotate(0deg);
}

/* Decorative Corner Elements */
.map-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-golden);
  z-index: 10;
}

.map-corner--top-left {
  top: -2px;
  left: -2px;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 8px;
}

.map-corner--top-right {
  top: -2px;
  right: -2px;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 8px;
}

.map-corner--bottom-left {
  bottom: -2px;
  left: -2px;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 8px;
}

.map-corner--bottom-right {
  bottom: -2px;
  right: -2px;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 8px;
}

.map-corner::before {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--color-golden);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 8px rgba(220, 202, 135, 0.6);
}

/* Map Information Panel */
.map-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.map-info-card {
  background: rgba(15, 40, 70, 0.8);
  border: 1px solid rgba(220, 202, 135, 0.2);
  border-radius: 8px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.map-info-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.map-info-icon {
  color: var(--color-golden);
  font-size: 1.5rem;
}

.map-info-header h3 {
  font-family: var(--font-base);
  color: #fff;
  font-size: 1.5rem;
  font-weight: 400;
  margin: 0;
}

.map-info-details .address {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.map-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.map-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background: var(--color-golden);
  color: var(--color-black);
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid var(--color-golden);
}

.map-action-btn:hover {
  background: transparent;
  color: var(--color-golden);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 202, 135, 0.3);
}

.map-action-btn--secondary {
  background: transparent;
  color: var(--color-golden);
  border: 2px solid var(--color-golden);
}

.map-action-btn--secondary:hover {
  background: var(--color-golden);
  color: var(--color-black);
}

/* Map Features */
.map-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.map-feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(220, 202, 135, 0.1);
  border-radius: 6px;
  border-left: 3px solid var(--color-golden);
}

.feature-icon {
  font-size: 1.2rem;
  min-width: 24px;
  text-align: center;
}

.map-feature span {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 0.9rem;
}



/* Responsive Styles */
@media screen and (max-width: 1024px) {
  .contact-main-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .contact-form-section,
  .contact-info-section {
    max-width: 100%;
  }

  .working-hours-section {
    order: -1;
  }

  .contact-info-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
  }

  /* Map responsive styles */
  .map-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .map-frame {
    height: 400px;
  }

  .map-title {
    font-size: 2.5rem;
  }
}

@media screen and (max-width: 768px) {
  .contact-header {
    padding: 3rem 1rem 2rem;
    text-align: center;
  }

  .contact-main-title {
    font-size: 3rem;
  }

  .contact-main-content {
    padding: 1rem;
    gap: 2rem;
  }

  .working-hours-card {
    padding: 2rem 1.5rem;
    min-width: auto;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
  }

  .contact-info-section {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
  }

  /* Map responsive styles for tablets */
  .contact-map-section {
    padding: 3rem 0;
  }

  .contact-map-container {
    padding: 0 1rem;
  }

  .map-frame {
    height: 350px;
    padding: 0.5rem;
  }

  .map-golden-border {
    padding: 0.5rem;
  }

  .map-info-card {
    padding: 1.5rem;
  }

  .map-actions {
    flex-direction: row;
    gap: 0.5rem;
  }

  .map-action-btn {
    flex: 1;
    justify-content: center;
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
}

@media screen and (max-width: 576px) {
  .contact-main-title {
    font-size: 2.5rem;
  }

  .contact-main-description {
    font-size: 1rem;
  }

  .contact-main-content {
    gap: 1.5rem;
  }

  .working-hours-card {
    padding: 1.5rem 1rem;
  }

  .working-hours-card h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .contact-info-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-form-section {
    max-width: 100%;
  }

  /* Map responsive styles for mobile */
  .map-title {
    font-size: 2rem;
  }

  .map-description {
    font-size: 1rem;
  }

  .map-frame {
    height: 300px;
    padding: 0.25rem;
  }

  .map-golden-border {
    padding: 0.25rem;
    border-width: 2px;
  }

  .map-corner {
    width: 15px;
    height: 15px;
  }

  .map-corner::before {
    width: 4px;
    height: 4px;
  }

  .map-info-card {
    padding: 1rem;
  }

  .map-info-header h3 {
    font-size: 1.2rem;
  }

  .map-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .map-action-btn {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .map-features {
    gap: 0.8rem;
  }

  .map-feature {
    padding: 0.8rem;
  }

  .map-feature span {
    font-size: 0.8rem;
  }
}