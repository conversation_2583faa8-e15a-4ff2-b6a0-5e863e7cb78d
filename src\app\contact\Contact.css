/* Main contact page layout */
.app__contactpage {
  min-height: 100vh;
  background: #1a2332;
  color: #fff;
  padding: 0;
}

/* Header Section */
.contact-header {
  padding: 4rem 2rem 2rem;
  text-align: left;
  max-width: 1200px;
  margin: 0 auto;
}

.contact-main-title {
  font-family: var(--font-base);
  font-size: 4rem;
  font-weight: 400;
  color: #fff;
  margin-bottom: 2rem;
  line-height: 1.1;
}

.contact-main-description {
  font-family: var(--font-alt);
  font-size: 1.1rem;
  line-height: 1.6;
  color: #ccc;
  max-width: 600px;
}

/* Main Content Layout */
.contact-main-content {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  align-items: start;
}

/* Contact Form Section */
.contact-form-section {
  max-width: 350px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #fff;
  margin-bottom: 0.5rem;
  font-weight: 400;
}

.form-group input,
.form-group textarea {
  background: transparent;
  border: none;
  border-bottom: 1px solid #555;
  color: #fff;
  padding: 0.8rem 0;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-bottom-color: #E8D58E;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666;
  font-size: 0.85rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.submit-btn {
  background: #d4621a;
  color: #fff;
  border: none;
  padding: 0.8rem 2rem;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  background: #b8541a;
}

.form-success {
  background: rgba(20, 50, 20, 0.5);
  border: 1px solid rgba(50, 220, 50, 0.3);
  padding: 1.5rem;
  border-radius: 4px;
  text-align: center;
}

/* Working Hours Section */
.working-hours-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.working-hours-card {
  background: #d4621a;
  padding: 2.5rem 2rem;
  border-radius: 4px;
  text-align: center;
  min-width: 280px;
}

.working-hours-card h2 {
  font-family: var(--font-base);
  font-size: 1.8rem;
  color: #fff;
  margin-bottom: 2rem;
  font-weight: 400;
}

.hours-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
}

.hours-item:last-child {
  margin-bottom: 0;
}

.day {
  font-family: var(--font-alt);
  font-size: 1rem;
  color: #fff;
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.time {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #fff;
  opacity: 0.9;
}

/* Contact Information Section */
.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 350px;
}

.contact-info-item {
  display: flex;
  flex-direction: column;
}

.contact-info-item h3 {
  font-family: var(--font-alt);
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0.8rem;
  font-weight: 500;
}

.contact-info-item p {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 0.3rem;
  line-height: 1.4;
}

.contact-info-item p:last-child {
  margin-bottom: 0;
}



/* Responsive Styles */
@media screen and (max-width: 1024px) {
  .contact-main-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .contact-form-section,
  .contact-info-section {
    max-width: 100%;
  }

  .working-hours-section {
    order: -1;
  }

  .contact-info-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
  }
}

@media screen and (max-width: 768px) {
  .contact-header {
    padding: 3rem 1rem 2rem;
    text-align: center;
  }

  .contact-main-title {
    font-size: 3rem;
  }

  .contact-main-content {
    padding: 1rem;
    gap: 2rem;
  }

  .working-hours-card {
    padding: 2rem 1.5rem;
    min-width: auto;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
  }

  .contact-info-section {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
  }
}

@media screen and (max-width: 576px) {
  .contact-main-title {
    font-size: 2.5rem;
  }

  .contact-main-description {
    font-size: 1rem;
  }

  .contact-main-content {
    gap: 1.5rem;
  }

  .working-hours-card {
    padding: 1.5rem 1rem;
  }

  .working-hours-card h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .contact-info-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-form-section {
    max-width: 100%;
  }
}