/* Main contact page layout */
.app__contactpage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #0A1622;
  position: relative;
}

.app__contactpage-heading {
  text-align: center;
  margin-bottom: 3rem;
  padding: 4rem 2rem 2rem;
  position: relative;
  z-index: 2;
  border-bottom: 1px solid rgba(232, 213, 142, 0.18);
}

.app__contactpage-heading h1 {
  text-shadow: 0 3px 10px rgba(12, 28, 53, 0.5);
}

.app__contactpage-back-link {
  margin-top: 1rem;
}

.app__contactpage-back-link a {
  color: #E8D58E;
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__contactpage-back-link a:hover {
  color: #fff;
}

/* Contact showcase section */
.app__contact-showcase {
  padding: 2rem 0;
  background: #0A1622;
}

.contact-showcase-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 3rem;
  align-items: center;
  min-height: 600px;
}

.contact-showcase:last-child {
  margin-bottom: 0;
}

.contact-showcase--reverse {
  direction: rtl;
}

.contact-showcase--reverse > * {
  direction: ltr;
}

.contact-showcase_image {
  position: relative;
  height: 600px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.contact-showcase_image img {
  transition: transform 0.8s ease;
}

.contact-showcase:hover .contact-showcase_image img {
  transform: scale(1.05);
}

.contact-showcase_image-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(10, 22, 34, 0.3) 0%, rgba(10, 22, 34, 0.1) 100%);
  z-index: 2;
}

.contact-showcase_content {
  padding: 3rem;
  display: flex;
  align-items: center;
  min-height: 600px;
}

.contact-showcase_text {
  width: 100%;
}

.contact-subtitle {
  font-family: var(--font-alt);
  color: #E8D58E;
  font-size: 0.9rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  margin-bottom: 1rem;
  font-weight: 300;
}

.contact-title {
  font-family: var(--font-base);
  color: #fff;
  font-size: 3.2rem;
  line-height: 1.1;
  margin-bottom: 2rem;
  font-weight: 400;
  letter-spacing: -1px;
}

.contact-description {
  font-family: var(--font-alt);
  color: #f1f1f1;
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 3rem;
  max-width: 500px;
}

.contact-details {
  margin-bottom: 3rem;
}

.contact-detail-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.contact-detail-icon {
  color: #E8D58E;
  font-size: 1.2rem;
  min-width: 20px;
}

.contact-detail-content {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.contact-detail-label {
  font-family: var(--font-alt);
  color: #E8D58E;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-detail-value {
  font-family: var(--font-base);
  color: #fff;
  font-size: 1.1rem;
}

.contact-cta {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: transparent;
  border: 2px solid #E8D58E;
  color: #E8D58E;
  padding: 1rem 2.5rem;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-cta:hover {
  background: #E8D58E;
  color: #0A1622;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(232, 213, 142, 0.3);
}

.cta-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.contact-cta:hover .cta-arrow {
  transform: translateX(5px);
}

/* Contact Form Section */
.app__contact-form-section {
  padding: 2rem 0;
  background: #0A1622;
}

.contact-form-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 600px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-form-showcase_image {
  position: relative;
  height: 600px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.contact-form-showcase_image img {
  transition: transform 0.8s ease;
}

.contact-form-showcase:hover .contact-form-showcase_image img {
  transform: scale(1.05);
}

.contact-form-showcase_image-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(10, 22, 34, 0.3) 0%, rgba(10, 22, 34, 0.1) 100%);
  z-index: 2;
}

.contact-form-showcase_content {
  padding: 3rem;
  display: flex;
  align-items: center;
  min-height: 600px;
}

.contact-form-showcase_text {
  width: 100%;
}

.modern-contact-form {
  margin-top: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #E8D58E;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  background: rgba(10, 22, 34, 0.7);
  border: 1px solid rgba(232, 213, 142, 0.2);
  border-radius: 4px;
  color: #fff;
  font-family: var(--font-alt);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #E8D58E;
  background: rgba(10, 22, 34, 0.9);
  box-shadow: 0 0 0 2px rgba(232, 213, 142, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(170, 170, 170, 0.7);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-success {
  background: rgba(20, 50, 20, 0.5);
  border: 1px solid rgba(50, 220, 50, 0.3);
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  margin-top: 2rem;
}

/* Quote Section */
.app__contactpage-quote {
  margin: 3rem 0;
  padding: 0 2rem;
}

.quote-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 3rem;
  background: rgba(10, 22, 34, 0.8);
  border-radius: 8px;
  position: relative;
  border: 1px solid rgba(232, 213, 142, 0.1);
}

.quote-container::before {
  content: "\201C";
  position: absolute;
  top: 10px;
  left: 20px;
  font-size: 150px;
  font-family: var(--font-base);
  color: #E8D58E;
  opacity: 0.2;
  line-height: 1;
}

.quote-icon {
  margin-bottom: 1.5rem;
}

.quote-container p:first-of-type {
  font-style: italic;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: #f1f1f1;
}

.quote-container p:last-of-type {
  color: #E8D58E;
  position: relative;
  display: inline-block;
}

.quote-container p:last-of-type::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 1px;
  background: linear-gradient(to right, transparent, #E8D58E, transparent);
}

/* Responsive Styles */
@media screen and (max-width: 1150px) {
  .app__contactpage-heading {
    padding: 3rem 2rem 2rem;
  }
}

@media screen and (max-width: 850px) {
  .contact-showcase,
  .contact-form-showcase {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .contact-showcase--reverse {
    direction: ltr;
  }

  .contact-showcase_image,
  .contact-form-showcase_image {
    height: 400px;
  }

  .contact-showcase_content,
  .contact-form-showcase_content {
    padding: 2rem;
    min-height: auto;
  }

  .contact-title {
    font-size: 2.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .contact-showcase-container,
  .contact-form-showcase {
    padding: 0 1rem;
  }
}

@media screen and (max-width: 576px) {
  .app__contactpage-heading {
    padding: 2rem 1rem 1rem;
  }

  .contact-showcase-container,
  .contact-form-showcase {
    padding: 0 1rem;
  }

  .contact-showcase,
  .contact-form-showcase {
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .contact-showcase_image,
  .contact-form-showcase_image {
    height: 300px;
  }

  .contact-showcase_content,
  .contact-form-showcase_content {
    padding: 1.5rem;
  }

  .contact-title {
    font-size: 2rem;
  }

  .contact-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .contact-cta {
    padding: 0.8rem 2rem;
    font-size: 0.8rem;
  }

  .quote-container {
    padding: 2rem 1rem;
  }

  .quote-container::before {
    font-size: 100px;
    top: 5px;
    left: 10px;
  }
}