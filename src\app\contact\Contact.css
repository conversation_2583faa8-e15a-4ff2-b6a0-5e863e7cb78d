.app__contactpage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: rgba(15, 40, 70, 0.95);
  position: relative;
  padding: 2rem 4rem;
}

.app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-black);
}

.app__contactpage-heading {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  border-bottom: 1px solid rgba(220, 202, 135, 0.18);
  padding-bottom: 1.5rem;
}

.app__contactpage-heading h1 {
  text-shadow: 0 3px 10px rgba(12, 28, 53, 0.5);
}

.app__contactpage-back-link {
  margin-top: 1rem;
}

.app__contactpage-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__contactpage-back-link a:hover {
  color: var(--color-white);
}

/* Container Layout */
.app__contactpage-container {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  margin: 2rem 0;
}

/* Info Section */
.app__contactpage-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-card {
  background: rgba(15, 40, 70, 0.92);
  padding: 2rem;
  border-radius: 4px;
  border-left: 2px solid rgba(220, 202, 135, 0.18);
  transition: all 0.3s ease;
}

.contact-card:hover {
  background: rgba(15, 40, 70, 0.96);
  transform: translateY(-5px);
  border-left: 2px solid rgba(220, 202, 135, 0.4);
}

.contact-card h2 {
  color: var(--color-golden);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.contact-card h2::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, var(--color-golden), transparent);
}

.contact-info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px dotted rgba(220, 202, 135, 0.12);
}

.contact-icon {
  color: var(--color-golden);
  font-size: 1.2rem;
  margin-right: 1rem;
  margin-top: 0.3rem;
}

.contact-info-item h3 {
  color: var(--color-white);
  font-weight: 600;
  margin-bottom: 0.3rem;
  font-size: 1rem;
}

.contact-social {
  margin-top: 2rem;
}

.contact-social h3 {
  color: var(--color-white);
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.contact-social-icons {
  display: flex;
  gap: 1.5rem;
}

.social-icon {
  color: var(--color-white);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.social-icon:hover {
  color: var(--color-golden);
  transform: scale(1.1);
}

.contact-map {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(220, 202, 135, 0.12);
}

.map-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.map-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.contact-map:hover .map-image {
  opacity: 1;
}

.contact-map:hover .map-overlay {
  opacity: 1;
}

/* Form Section */
.app__contactpage-form {
  flex: 1;
}

.contact-form-container {
  background: rgba(15, 40, 70, 0.92);
  padding: 2rem;
  border-radius: 4px;
  border-left: 2px solid rgba(220, 202, 135, 0.18);
  transition: all 0.3s ease;
}

.contact-form-container:hover {
  background: rgba(15, 40, 70, 0.96);
  transform: translateY(-5px);
  border-left: 2px solid rgba(220, 202, 135, 0.4);
}

.contact-form-container h2 {
  color: var(--color-golden);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.contact-form-container h2::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(to right, var(--color-golden), transparent);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-golden);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  background: rgba(15, 40, 70, 0.7);
  border: 1px solid rgba(220, 202, 135, 0.18);
  border-radius: 4px;
  color: var(--color-white);
  font-family: var(--font-alt);
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-golden);
  background: rgba(15, 40, 70, 0.8);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(170, 170, 170, 0.7);
}

.custom__button {
  background-color: rgba(218, 179, 38, 0.85);
  color: var(--color-black);
  padding: 0.75rem 2rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  border: 1px solid rgba(220, 202, 135, 0.9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  font-weight: 700;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
}

.custom__button:hover {
  background-color: transparent;
  color: var(--color-golden);
}

.form-success {
  background: rgba(20, 50, 20, 0.5);
  border: 1px solid rgba(50, 220, 50, 0.3);
  padding: 1.5rem;
  border-radius: 4px;
  text-align: center;
}

/* Quote Section */
.app__contactpage-quote {
  margin: 3rem 0;
}

.quote-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 2rem;
  background: rgba(15, 40, 70, 0.92);
  border-radius: 4px;
  position: relative;
}

.quote-container::before {
  content: "\201C";
  position: absolute;
  top: 10px;
  left: 20px;
  font-size: 150px;
  font-family: var(--font-base);
  color: var(--color-golden);
  opacity: 0.2;
  line-height: 1;
}

.quote-icon {
  margin-bottom: 1.5rem;
}

.quote-container p:first-of-type {
  font-style: italic;
  line-height: 1.8;
  margin-bottom: 1.5rem;
}

.quote-container p:last-of-type {
  color: var(--color-golden);
  position: relative;
  display: inline-block;
}

.quote-container p:last-of-type::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-golden), transparent);
}

/* Responsive Styles */
@media screen and (max-width: 1150px) {
  .app__contactpage {
    padding: 2rem;
  }
  
  .app__contactpage-container {
    flex-direction: column;
  }
  
  .app__contactpage-info,
  .app__contactpage-form {
    width: 100%;
  }
}

@media screen and (max-width: 850px) {
  .contact-card,
  .contact-form-container {
    padding: 1.5rem;
  }
}

@media screen and (max-width: 650px) {
  .app__contactpage {
    padding: 1.5rem;
  }
  
  .contact-card h2,
  .contact-form-container h2 {
    font-size: 1.5rem;
  }
  
  .custom__button {
    width: 100%;
  }
} 