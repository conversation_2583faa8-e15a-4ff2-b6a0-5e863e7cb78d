"use client";

import React from "react";
import Link from "next/link";

const PrivateEventsContact = () => {
  return (
    <section className="app__privateevents-contact">
      <div className="app__privateevents-contact_inner">
        <div className="app__privateevents-contact_heading">
          <h2>Ready to Book Your Event?</h2>
          <p>
            Transform your special occasion into an unforgettable experience. Our dedicated events team is ready to help you create the perfect gathering in our elegant private dining spaces.
          </p>
        </div>

        <div className="app__privateevents-contact_features">
          <div className="contact-feature">
            <div className="contact-feature_icon">📅</div>
            <h3>Easy Booking</h3>
            <p>Simple online reservation system for all your private event needs</p>
          </div>

          <div className="contact-feature">
            <div className="contact-feature_icon">👨‍🍳</div>
            <h3>Custom Menus</h3>
            <p>Personalized dining experiences crafted by our executive chef</p>
          </div>

          <div className="contact-feature">
            <div className="contact-feature_icon">🎉</div>
            <h3>Full Service</h3>
            <p>Complete event coordination from planning to execution</p>
          </div>
        </div>

        <div className="app__privateevents-contact_cta">
          <Link href="/book" className="contact-cta-button">
            <span>Book Your Private Event</span>
            <div className="button-arrow">→</div>
          </Link>

          <div className="contact-info">
            <p>Questions? Call us at <strong>(555) 123-4567</strong></p>
            <p>or email <strong><EMAIL></strong></p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PrivateEventsContact; 