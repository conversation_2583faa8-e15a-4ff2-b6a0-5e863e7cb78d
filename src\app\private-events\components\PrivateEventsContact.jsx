"use client";

import React from "react";

const PrivateEventsContact = () => {
  return (
    <section className="app__privateevents-contact">
      <div className="app__privateevents-contact_inner">
        <div className="app__privateevents-contact_heading">
          <h2>Plan Your Event</h2>
          <p>
            Ready to host a memorable gathering? Contact us to discuss your event details, check availability, or schedule a tour of our private dining spaces.
          </p>
        </div>
        
        <form className="contact-form">
          <div className="form-field">
            <label htmlFor="name">Name</label>
            <input type="text" id="name" name="name" required />
          </div>
          
          <div className="form-field">
            <label htmlFor="email">Email</label>
            <input type="email" id="email" name="email" required />
          </div>
          
          <div className="form-field">
            <label htmlFor="phone">Phone</label>
            <input type="tel" id="phone" name="phone" />
          </div>
          
          <div className="form-field">
            <label htmlFor="eventDate">Event Date</label>
            <input type="date" id="eventDate" name="eventDate" />
          </div>
          
          <div className="form-field">
            <label htmlFor="guestCount">Guest Count</label>
            <input type="number" id="guestCount" name="guestCount" min="1" />
          </div>
          
          <div className="form-field">
            <label htmlFor="eventType">Event Type</label>
            <select id="eventType" name="eventType">
              <option value="">Select an event type</option>
              <option value="corporate">Corporate Event</option>
              <option value="wedding">Wedding Reception</option>
              <option value="birthday">Birthday Celebration</option>
              <option value="anniversary">Anniversary</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div className="form-field full-width">
            <label htmlFor="message">Additional Details</label>
            <textarea id="message" name="message"></textarea>
          </div>
          
          <div className="form-submit">
            <button type="submit">Submit Inquiry</button>
          </div>
        </form>
      </div>
    </section>
  );
};

export default PrivateEventsContact; 