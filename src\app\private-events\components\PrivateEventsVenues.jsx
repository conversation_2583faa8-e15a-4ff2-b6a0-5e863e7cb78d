"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { images } from "@/constants";

const PrivateEventsVenues = () => {
  // Venue data
  const venues = [
    {
      id: 1,
      name: "The Rose Room",
      subtitle: "Intimate Elegance",
      image: images.gallery01,
      description: "Discover a world of refined dining where intimate gatherings become unforgettable experiences. This luxurious space offers an air of exclusivity with rich decor, soft ambient lighting, and personalized service that transforms every meal into a celebration.",
      capacity: "10-20 guests",
      features: ["Private entrance", "Custom menus", "Audio equipment"],
      cta: "Book Now"
    },
    {
      id: 2,
      name: "Outdoor Full Patio",
      subtitle: "Al Fresco Dining",
      image: images.gallery02,
      description: "Experience the perfect blend of nature and sophistication in our stunning outdoor venue. With ambient lighting creating a magical atmosphere and partial coverage ensuring comfort, this space is ideal for cocktail receptions and memorable celebrations under the stars.",
      capacity: "25-60 guests",
      features: ["Outdoor setting", "Heating options", "Evening lighting"],
      cta: "Book Now"
    },
    {
      id: 3,
      name: "Main Dining Room",
      subtitle: "Grand Celebrations",
      image: images.gallery03,
      description: "Transform your vision into reality with our exclusive main dining space. This sophisticated venue offers complete privacy and customization options, featuring exceptional decor and an atmosphere that elevates any gathering into an extraordinary event.",
      capacity: "50-100 guests",
      features: ["Full restaurant buyout", "Custom floor plan", "Audiovisual options"],
      cta: "Book Now"
    }
  ];

  return (
    <section className="app__privateevents-venues">
      <div className="venues-container">
        {venues.map((venue, index) => (
          <div key={venue.id} className={`venue-showcase ${index % 2 === 1 ? 'venue-showcase--reverse' : ''}`}>
            <div className="venue-showcase_image">
              <Image
                src={venue.image}
                alt={venue.name}
                fill
                style={{ objectFit: 'cover' }}
                quality={95}
              />
              <div className="venue-showcase_image-overlay"></div>
            </div>

            <div className="venue-showcase_content">
              <div className="venue-showcase_text">
                <div className="venue-subtitle">{venue.subtitle}</div>
                <h2 className="venue-title">{venue.name}</h2>
                <p className="venue-description">{venue.description}</p>

                <div className="venue-details">
                  <div className="venue-capacity">
                    <span className="capacity-label">Capacity:</span>
                    <span className="capacity-value">{venue.capacity}</span>
                  </div>

                  <div className="venue-features">
                    {venue.features.map((feature, featureIndex) => (
                      <span key={featureIndex} className="venue-feature">{feature}</span>
                    ))}
                  </div>
                </div>

                <Link href="/book" className="venue-cta">
                  <span>{venue.cta}</span>
                  <div className="cta-arrow">→</div>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default PrivateEventsVenues; 