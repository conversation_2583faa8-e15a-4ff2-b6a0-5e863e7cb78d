"use client";

import React from "react";
import Image from "next/image";
import { images } from "@/constants";

const PrivateEventsVenues = () => {
  // Venue data
  const venues = [
    {
      id: 1,
      name: "The Rose Room",
      image: images.gallery01,
      description: "An elegant private dining room with rich decor and soft lighting, perfect for intimate gatherings and special celebrations.",
      capacity: "10-20 guests",
      features: ["Private entrance", "Custom menus", "Audio equipment"]
    },
    {
      id: 2,
      name: "Outdoor Full Patio",
      image: images.gallery02,
      description: "A beautiful outdoor space with ambient lighting and partial coverage, ideal for cocktail receptions and special events in pleasant weather.",
      capacity: "25-60 guests",
      features: ["Outdoor setting", "Heating options", "Evening lighting"]
    },
    {
      id: 3,
      name: "Main Dining Room",
      image: images.gallery03,
      description: "Our stunning main dining space available for exclusive use, featuring sophisticated decor and an exceptional atmosphere for larger events.",
      capacity: "50-100 guests",
      features: ["Full restaurant buyout", "Custom floor plan", "Audiovisual options"]
    }
  ];

  return (
    <section className="app__privateevents-section">
      <div className="app__privateevents-section_heading">
        <h2 className="section-title">Private Dining Spaces</h2>
      </div>
      
      <div className="app__privateevents-venues_grid">
        {venues.map((venue) => (
          <div key={venue.id} className="venue-card">
            <div className="venue-card_image">
              <Image 
                src={venue.image} 
                alt={venue.name} 
                fill
                style={{ objectFit: 'cover' }}
              />
            </div>
            <div className="venue-card_overlay"></div>
            <div className="venue-card_content">
              <h3>{venue.name}</h3>
              <p>{venue.description}</p>
              <div className="venue-card_capacity">Capacity: {venue.capacity}</div>
              <div className="venue-card_details">
                {venue.features.map((feature, index) => (
                  <span key={index} className="venue-card_detail">{feature}</span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default PrivateEventsVenues; 