import images from './images';
import data from './data';

// Single format references for backward compatibility
const meal = 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756003/indoor_wyj3ga.mp4';
const herovideo = 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756008/herovideo_s5rjoj.mp4';

// Multi-format references with MP4 and WebM for maximum compatibility
const mealFormats = {
  mp4: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756003/indoor_wyj3ga.mp4',
  webm: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756006/indoor_hjbfpv.webm'  // WebM format for broader browser compatibility
};

const herovideoFormats = {
  mp4: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756008/herovideo_s5rjoj.mp4',
  webm: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756002/herovideo_z8q8hv.webm'  // WebM format for broader browser compatibility
};

export { 
  images, 
  meal, 
  data, 
  herovideo,
  mealFormats,
  herovideoFormats
};
