"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>ram,
  BsArrowLeftShort,
  BsArrowRightShort,
} from "react-icons/bs";
import Image from "next/image";

import { SubHeading } from "../../components";
import { images } from "../../constants";
import "./Gallery.css";
import Link from "next/link";

const Gallery = () => {
  const scrollRef = React.useRef(null);

  const scroll = (direction) => {
    const { current } = scrollRef;

    if (direction === "left") {
      current.scrollLeft -= 300;
    } else {
      current.scrollLeft += 300;
    }
  };

  return (
    <div className="app__gallery flex__center" id="events">
      <div className="app__gallery-content">
        <SubHeading title="Events" />
        <h1 className="headtext__cormorant">Special Occasions</h1>
        <p
          className="p__opensans"
          style={{ color: "#AAAAAA", marginTop: "2rem" }}
        >
          Host your special events with us. From private dinners to corporate gatherings, 
          we create memorable experiences tailored to your needs.
        </p>
        <Link href="/private-events" className="custom__button">
          Book Event
        </Link>
      </div>
      <div className="app__gallery-images">
        <div className="app__gallery-images_container" ref={scrollRef}>
          {[
            images.gallery01,
            images.gallery02,
            images.gallery03,
            images.gallery04,
          ].map((image, index) => (
            <div
              className="app__gallery-images_card flex__center"
              key={`gallery_image-${index + 1}`}
            >
              <Image 
                src={image} 
                alt="gallery_image"
                width={301}
                height={447}
                style={{ objectFit: 'cover' }}
              />
              <BsInstagram className="gallery__image-icon" />
            </div>
          ))}
        </div>
        <div className="app__gallery-images_arrows">
          <BsArrowLeftShort
            className="gallery__arrow-icon"
            onClick={() => scroll("left")}
          />
          <BsArrowRightShort
            className="gallery__arrow-icon"
            onClick={() => scroll("right")}
          />
        </div>
      </div>
    </div>
  );
};

export default Gallery;
