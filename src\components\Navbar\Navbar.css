.app__navbar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-black);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.app__navbar.scrolled {
  padding: 1.5rem 2rem;
  background: rgba(15, 40, 70, 0.95);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.app__navbar-logo {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.app__navbar-logo img {
  width: 100px;
  height: auto;
}

.app__navbar-links {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  list-style: none;
}

.app__navbar-links li {
  margin: 0 1rem;
  cursor: pointer;
}

.app__navbar-links li:hover {
  color: var(--color-grey);
}

.app__navbar-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.private-events-button {
  margin-right: 1rem;
  color: var(--color-white);
  padding: 0.5rem 1rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  border: 1px solid var(--color-golden);
}

.private-events-button:hover {
  background-color: rgba(220, 202, 135, 0.1);
  color: var(--color-golden);
}

.private-events-button span {
  font-weight: 500;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
}

.book-button {
  background-color: rgba(218, 179, 38, 0.85);
  color: var(--color-black);
  padding: 0.5rem 1.5rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  border: 1px solid rgba(220, 202, 135, 0.9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.book-button:hover {
  background-color: transparent;
  color: var(--color-golden);
  transform: scale(1.05);
}

.book-button span {
  font-weight: 700;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
}

.app__navbar-smallscreen {
  display: none;
}

.hamburgerMenu {
  cursor: pointer;
}

.app__navbar-smallscreen_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(15, 40, 70, 0.98);
  transition: 0.5s ease;
  flex-direction: column;
  z-index: 5;
}

.mobile-menu-logo {
  margin: 1rem 0 2rem;
}

.app__navbar-smallscreen_overlay .overlay__close {
  font-size: 27px;
  color: var(--color-golden);
  cursor: pointer;
  position: absolute;
  top: 20px;
  right: 20px;
}

.app__navbar-smallscreen_links {
  list-style: none;
}

.app__navbar-smallscreen_links li {
  margin: 2rem;
  cursor: pointer;
  color: var(--color-golden);
  font-size: 2rem;
  text-align: center;
  font-family: var(--font-base);
}

.app__navbar-smallscreen_links li:hover {
  color: var(--color-white);
}

.app__navbar-smallscreen_actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 1.5rem;
}

.smallscreen-private-events-button {
  color: var(--color-golden);
  padding: 0.75rem 2rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
  border: 1px solid rgba(220, 202, 135, 0.9);
}

.smallscreen-private-events-button:hover {
  background-color: rgba(220, 202, 135, 0.1);
}

.smallscreen-private-events-button span {
  font-weight: 500;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 18px;
}

.smallscreen-book-button {
  background-color: rgba(218, 179, 38, 0.85);
  color: var(--color-black);
  padding: 0.75rem 2rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
  border: 1px solid rgba(220, 202, 135, 0.9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.smallscreen-book-button:hover {
  background-color: transparent;
  color: var(--color-golden);
}

.smallscreen-book-button span {
  font-weight: 700;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 18px;
}

@media screen and (min-width: 2000px) {
  .app__navbar-logo img {
    width: 160px;
  }
}

@media screen and (max-width: 1150px) {
  .app__navbar-links {
    display: none;
  }

  .app__navbar {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
  }

  .app__navbar-logo {
    grid-column: 1;
  }

  .app__navbar-actions {
    grid-column: 2;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .app__navbar-smallscreen {
    grid-column: 3;
    display: flex;
    align-items: center;
    margin-left: 1rem;
  }

  .app__navbar-logo img {
    width: 110px;
  }
}

@media screen and (max-width: 950px) {
  .app__navbar {
    padding: 1rem 1.5rem;
  }
  
  .app__navbar.scrolled {
    padding: 1rem 1.5rem;
  }
  
  .app__navbar-logo img {
    width: 100px;
  }
  
  .private-events-button {
    margin-right: 0.5rem;
    padding: 0.4rem 1rem;
  }
  
  .private-events-button span {
    font-size: 14px;
    line-height: 24px;
  }
  
  .book-button {
    padding: 0.4rem 1.2rem;
  }
  
  .book-button span {
    font-size: 14px;
    line-height: 24px;
  }
}

@media screen and (max-width: 768px) {
  .app__navbar {
    padding: 1rem 1.25rem;
  }
  
  .app__navbar.scrolled {
    padding: 1rem 1.25rem;
  }
  
  .app__navbar-logo img {
    width: 85px;
  }
  
  .private-events-button {
    padding: 0.35rem 0.9rem;
  }
  
  .private-events-button span {
    font-size: 13px;
    line-height: 22px;
  }
  
  .book-button {
    padding: 0.35rem 1rem;
  }
  
  .book-button span {
    font-size: 13px;
    line-height: 22px;
  }
  
  .hamburgerMenu {
    font-size: 24px !important;
  }
  
  .mobile-menu-logo {
    margin: 0.5rem 0 1.5rem;
  }
}

@media screen and (max-width: 650px) {
  .app__navbar {
    padding: 1rem;
  }

  .app__navbar.scrolled {
    padding: 1rem 1rem;
  }

  .app__navbar-actions {
    display: none;
  }

  .app__navbar-logo img {
    width: 80px;
  }
}

@media screen and (max-width: 480px) {
  .app__navbar-logo img {
    width: 70px;
  }
  
  .mobile-menu-logo img {
    width: 90px !important;
    height: auto !important;
  }
  
  .app__navbar-smallscreen_links li {
    margin: 1.5rem;
    font-size: 1.8rem;
  }
}
