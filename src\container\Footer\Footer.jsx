import React from "react";
import { <PERSON><PERSON>ace<PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiInstagram } from "react-icons/fi";
import Image from "next/image";

import { FooterOverlay, Newsletter } from "../../components";
import { images } from "../../constants";
import "./Footer.css";

const Footer = () => (
  <div className="app__footer section__padding" id="login">
    <FooterOverlay />
    <Newsletter />

    <div className="app__footer-links">
      <div className="app__footer-links_contact">
        <h1 className="app__footer-headtext">Contact Us</h1>
        <p className="p__opensans">9 W 53rd St, New York, NY 10019, USA</p>
        <p className="p__opensans">+1 212-344-1230</p>
        <p className="p__opensans">+1 212-555-1230</p>
      </div>

      <div className="app__footer-links_logo">
        <div className="logo-container">
          <Image 
            src="/assets/BLUBRASSERIE.svg" 
            alt="footer_logo" 
            width={150} 
            height={43} 
            loading="lazy"
            style={{ height: 'auto' }}
          />
        </div>
        <p className="p__opensans">
          &quot;The best way to find yourself is to lose yourself in the service
          of others.&quot;
        </p>
        <div className="spoon-container" style={{ marginTop: 15 }}>
          <Image
            src={images.spoon}
            alt="spoon"
            width={45}
            height={15}
            loading="lazy"
            className="spoon__img"
          />
        </div>
        <div className="app__footer-links_icons">
          <FiFacebook />
          <FiTwitter />
          <FiInstagram />
        </div>
      </div>

      <div className="app__footer-links_work">
        <h1 className="app__footer-headtext">Working Hours</h1>
        <p className="p__opensans">Monday-Friday:</p>
        <p className="p__opensans">08:00 am - 12:00 am</p>
        <p className="p__opensans">Saturday-Sunday:</p>
        <p className="p__opensans">07:00 am - 11:00 pm</p>
      </div>
    </div>

    <div className="footer__copyright">
      <p className="p__opensans">2023 BLUBRASSERIE. All Rights reserved.</p>
    </div>
  </div>
);

export default Footer;
