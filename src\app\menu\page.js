"use client";

import React, { useState } from "react";
import { SubHeading, Navbar } from "@/components";
import Link from "next/link";
import "./Menu.css";

// Import menu components
import { 
  MenuTab, 
  DinnerMenu, 
  WineMenu, 
  DessertsMenu 
} from "./components";

const MenuPage = () => {
  const [activeTab, setActiveTab] = useState("dinner");
  
  const tabs = [
    { id: "dinner", label: "DINNER" },
    { id: "wine", label: "WINE" },
    { id: "desserts", label: "DESSERTS" }
  ];

  // Render content based on active tab
  const renderTabContent = () => {
    switch(activeTab) {
      case "dinner":
        return <DinnerMenu />;
      case "wine":
        return <WineMenu />;
      case "desserts":
        return <DessertsMenu />;
      default:
        return <DinnerMenu />;
    }
  };
  
  return (
    <>
      <Navbar />
      <div className="app__menupage app__bg">
        <div className="app__menupage-heading">
          <SubHeading title="Discover Our Culinary Delights" />
          <h1 className="headtext__cormorant">Our Menu</h1>
          <div className="app__menupage-back-link">
            <Link href="/" className="p__opensans">
              <span>← Back to Homepage</span>
            </Link>
          </div>
        </div>

        <div className="menu-tabs">
          {tabs.map(tab => (
            <MenuTab 
              key={tab.id}
              label={tab.label}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            />
          ))}
        </div>

        {renderTabContent()}
      </div>
    </>
  );
};

export default MenuPage; 