"use client";

import React, { Suspense } from "react";
import Link from "next/link";

import OptimizedVideo from "../../components/OptimizedVideo";
import { herovideoFormats } from "../../constants";
import "./Header.css";

const VideoSkeleton = () => (
  <div 
    className="video-skeleton"
    style={{
      width: '100%',
      height: '100%',
      backgroundColor: 'rgba(15, 40, 70, 0.7)',
      position: 'absolute',
      inset: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}
    aria-label="Loading video"
    role="status"
  >
    <div 
      style={{ 
        color: '#DCCA87', 
        textAlign: 'center',
        padding: '20px'
      }}
    >
      Loading hero video...
    </div>
  </div>
);

const HeaderVideo = () => (
  <OptimizedVideo 
    src={herovideoFormats}
    autoPlay={true}
    loop={true}
    muted={true}
    playsInline={true}
    priority={true}
    className="header-background-video"
  />
);

const Header = () => {
  return (
    <div className="app__header section__padding" id="home">
      <div className="app__header-video-background">
        <Suspense fallback={<VideoSkeleton />}>
          <HeaderVideo />
        </Suspense>
        <div className="frame-top-right"></div>
        <div className="frame-bottom-left"></div>
      </div>
      
      <div className="app__header-content">
        <div className="app__header-info">
          <div className="app__header-headline">
            <span className="regular-text">Discover</span>
            <span className="highlight-text">Fine</span>
            <span className="regular-text">Dining</span>
          </div>
          
          <h1 className="app__header-title">Exquisite Cuisine Experience</h1>
          
          <p className="p__opensans tagline">
            Award-winning restaurant serving gourmet dishes with locally-sourced ingredients
          </p>
          
          <div className="app__header-buttons">
            <Link href="/menu">
              <button type="button" className="custom__button">
                Our Menu
              </button>
            </Link>
            <Link href="/book">
              <button type="button" className="custom__button book-now">
                Reserve Now
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
