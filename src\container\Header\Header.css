.app__header {
  position: relative;
  background-color: transparent; /* Remove navy blue background */
  margin-top: -2rem;
  padding-top: 0;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
}

.app__header-video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.header-background-video {
  width: 100%;
  height: 100%;
}

.header-background-video video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 1; /* Restore full video opacity */
}

.app__header-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  padding-top: -2rem;
}

.app__header-info {
  max-width: 750px;
  padding: 2rem;
  text-align: center;
}

.app__header-headline {
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
  font-size: 23px;
  line-height: 1.1;
  font-weight: 600;
  letter-spacing: 0.04em;
  font-family: var(--font-base);
}

.app__header-title {
  font-family: var(--font-base);
  color: var(--color-golden);
  font-weight: 700;
  letter-spacing: 0.04em;
  text-transform: capitalize;
  line-height: 90px;
  font-size: 90px;
  margin: 1rem 0 2rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.regular-text {
  color: #ffffff;
  margin-right: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.highlight-text {
  color: var(--color-golden);
  margin-right: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.tagline {
  font-size: 16px;
  color: #ffffff;
  margin: 1.5rem 0;
  letter-spacing: 0.04em;
  max-width: 600px;
  margin: 0 auto 2rem auto;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  background-color: rgba(0, 0, 0, 0.4);
  padding: 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

.app__header-location {
  color: #ffffff;
  font-size: 16px;
  letter-spacing: 0.2em;
  margin: 2rem 0;
}

.frame-top-right {
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 150px;
  height: 150px;
  border-top: 4px solid var(--color-golden);
  border-right: 4px solid var(--color-golden);
  z-index: 1;
}

.frame-bottom-left {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  width: 150px;
  height: 150px;
  border-bottom: 4px solid var(--color-golden);
  border-left: 4px solid var(--color-golden);
  z-index: 1;
}

.app__header-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2.5rem;
}

.app__header-buttons .custom__button {
  background-color: transparent;
  color: #ffffff;
  font-family: var(--font-base);
  font-weight: 500;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
  padding: 0.8rem 2rem;
  border-radius: 0;
  border: 1px solid #ffffff;
  outline: none;
  cursor: pointer;
  min-width: 150px;
  transition: all 0.3s ease;
}

.app__header-buttons .custom__button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.app__header-buttons .book-now {
  background-color: var(--color-golden);
  color: var(--color-black);
  border: none;
}

.app__header-buttons .book-now:hover {
  background-color: rgba(220, 202, 135, 0.9);
  color: var(--color-black);
}

@media screen and (min-width: 2000px) {
  .app__header-headline {
    font-size: 32px;
  }
  
  .app__header-title {
    font-size: 180px;
    line-height: 210px;
  }
  
  .tagline {
    font-size: 28px;
  }
  
  .frame-top-right,
  .frame-bottom-left {
    width: 250px;
    height: 250px;
    border-width: 6px;
  }
  
  .app__header-info {
    max-width: 900px;
  }
}

/* Tablet-specific optimizations */
@media screen and (min-width: 768px) and (max-width: 1150px) {
  .app__header {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .app__header-content {
    align-items: center;
    justify-content: center;
    height: 100%;
    padding-top: 0;
    width: 100%;
  }
  
  .app__header-info {
    max-width: 650px;
    padding: 2.5rem;
    background-color: rgba(0, 0, 0, 0.55);
    border-radius: 8px;
    margin: 0 auto;
    position: relative;
    top: 0;
    width: 85%;
  }
  
  .app__header-headline {
    font-size: 22px;
    justify-content: center;
    display: none; /* Hide the subtitle to center main content better */
  }
  
  .app__header-title {
    font-size: 68px;
    line-height: 75px;
    margin: 0 0 1.2rem 0;
    text-align: center;
  }
  
  .tagline {
    font-size: 17px;
    background-color: transparent;
    padding: 0;
    max-width: 550px;
    text-align: center;
    margin: 0 auto 1.5rem auto;
    display: block;
  }
  
  .app__header-buttons {
    margin-top: 1.5rem;
    justify-content: center;
  }
  
  .app__header-buttons .custom__button {
    min-width: 140px;
    padding: 0.7rem 1.8rem;
  }
  
  .frame-top-right,
  .frame-bottom-left {
    width: 120px;
    height: 120px;
  }
}

@media screen and (max-width: 1150px) {
  .app__header-headline {
    font-size: 20px;
  }
  
  .app__header-title {
    font-size: 80px;
    line-height: 90px;
  }
  
  .tagline {
    font-size: 18px;
  }
  
  .frame-top-right,
  .frame-bottom-left {
    width: 120px;
    height: 120px;
  }
}

@media screen and (max-width: 850px) {
  .app__header {
    height: auto;
    min-height: 100vh;
  }
  
  .app__header-info {
    max-width: 550px;
    padding: 1.5rem;
    margin-top: 0;
  }
  
  .app__header-content {
    padding-top: 0;
    align-items: center;
  }
  
  .app__header-headline {
    font-size: 18px;
  }
  
  .app__header-title {
    font-size: 70px;
    line-height: 80px;
  }
  
  .tagline {
    font-size: 16px;
    margin: 1rem 0;
  }
  
  .app__header-buttons {
    flex-direction: row;
    gap: 1rem;
  }
  
  .app__header-buttons .custom__button {
    min-width: 130px;
    padding: 0.6rem 1.5rem;
    font-size: 15px;
  }
  
  .frame-top-right,
  .frame-bottom-left {
    width: 100px;
    height: 100px;
    border-width: 3px;
  }
}

@media screen and (max-width: 650px) {
  .app__header {
    margin-top: 0;
  }
  
  .app__header-content {
    justify-content: center;
    padding: 0 1rem;
  }
  
  .app__header-info {
    max-width: 450px;
    padding: 1.2rem;
    margin-top: 3rem;
  }
  
  .app__header-headline {
    font-size: 16px;
  }
  
  .regular-text, .highlight-text {
    margin-right: 10px;
  }
  
  .app__header-title {
    font-size: 55px;
    line-height: 65px;
    margin: 0.5rem 0 1rem 0;
  }
  
  .tagline {
    font-size: 15px;
    margin: 0.8rem 0;
  }
  
  .app__header-buttons {
    flex-direction: column;
    gap: 0.8rem;
    margin-top: 1.5rem;
  }
  
  .app__header-buttons .custom__button {
    min-width: 200px;
    padding: 0.5rem 1rem;
  }
  
  .frame-top-right,
  .frame-bottom-left {
    width: 80px;
    height: 80px;
    border-width: 3px;
  }
}

@media screen and (max-width: 450px) {
  .app__header {
    margin-top: 0;
  }
  
  .app__header-info {
    max-width: 320px;
    padding: 1rem;
    margin-top: 2rem;
  }
  
  .app__header-headline {
    font-size: 14px;
  }
  
  .regular-text, .highlight-text {
    margin-right: 8px;
  }
  
  .app__header-title {
    font-size: 42px;
    line-height: 50px;
  }
  
  .tagline {
    font-size: 14px;
    margin: 0.6rem 0;
  }
  
  .app__header-buttons .custom__button {
    min-width: 180px;
    font-size: 14px;
    padding: 0.4rem 0.8rem;
  }
  
  .frame-top-right,
  .frame-bottom-left {
    width: 60px;
    height: 60px;
    border-width: 2px;
  }
}
