"use client";

import React from "react";
import { MdOutlineRestaurantMenu } from "react-icons/md";
import Link from "next/link";
import Image from "next/image";

const MobileMenu = ({ isOpen, setIsOpen, pathname }) => {
  const isHomePage = pathname === "/";

  // Create proper navigation links based on current page
  const getNavLink = (section) => {
    if (isHomePage) {
      return `#${section}`;
    } else {
      return `/#${section}`;
    }
  };
  
  return isOpen ? (
    <div className="app__navbar-smallscreen_overlay flex__center slide-bottom">
      <MdOutlineRestaurantMenu
        className="overlay__close"
        fontSize={27}
        onClick={() => setIsOpen(false)}
      />
      <div className="mobile-menu-logo">
        <Link href="/" onClick={() => setIsOpen(false)}>
          <Image 
            src="/assets/BLUBRASSERIE.svg" 
            alt="BLUBRASSERIE logo" 
            width={100}
            height={29}
            priority
          />
        </Link>
      </div>
      <ul className="app__navbar-smallscreen_links">
        <li className="p__opensans">
          <Link href={getNavLink("menu")} onClick={() => setIsOpen(false)}>Menu</Link>
        </li>
        <li className="p__opensans">
          <Link href={getNavLink("about")} onClick={() => setIsOpen(false)}>About</Link>
        </li>
        <li className="p__opensans">
          <Link href={getNavLink("events")} onClick={() => setIsOpen(false)}>Events</Link>
        </li>
        <li className="p__opensans">
          <Link href={getNavLink("contact")} onClick={() => setIsOpen(false)}>Hours & Info</Link>
        </li>
      </ul>
      <div className="app__navbar-smallscreen_actions">
        <Link href="/private-events" className="smallscreen-private-events-button" onClick={() => setIsOpen(false)}>
          <span className="p__opensans">Private Events</span>
        </Link>
        <Link href="/book" className="smallscreen-book-button" onClick={() => setIsOpen(false)}>
          <span className="p__opensans">Reserve Table</span>
        </Link>
      </div>
    </div>
  ) : null;
};

export default MobileMenu; 