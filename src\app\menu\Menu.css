.app__menupage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: rgba(15, 40, 70, 0.95);
  position: relative;
  padding: 2rem 4rem;
  max-width: 100%;
  margin: 0 auto;
}

.app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-black);
}

.app__menupage-heading {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  border-bottom: 1px solid rgba(220, 202, 135, 0.3);
  padding-bottom: 1.5rem;
}

.app__menupage-heading h1 {
  text-shadow: 0 3px 10px rgba(12, 28, 53, 0.5);
}

.app__menupage-back-link {
  margin-top: 1rem;
}

.app__menupage-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__menupage-back-link a:hover {
  color: var(--color-white);
}

/* Menu Tabs Styling */
.menu-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.menu-tab {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--color-golden);
  color: var(--color-white);
  font-family: var(--font-base);
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  background: rgba(15, 40, 70, 0.85);
}

.menu-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-golden);
  transition: width 0.3s ease;
}

.menu-tab.active {
  background: rgba(15, 40, 70, 0.92);
  color: var(--color-golden);
  border-color: var(--color-golden);
}

.menu-tab.active::after {
  width: 100%;
}

.menu-tab:hover {
  background: rgba(15, 40, 70, 0.92);
  transform: translateY(-2px);
}

/* Menu Description */
.menu-description,
.brunch-description,
.dessert-description {
  text-align: center;
  max-width: 800px;
  margin: 2rem auto;
  line-height: 1.6;
  color: var(--color-white);
  background: rgba(15, 40, 70, 0.85);
  padding: 1.5rem;
  border-radius: 4px;
  border: 1px solid rgba(220, 202, 135, 0.12);
  box-shadow: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.menu-view-button {
  margin-top: 1rem;
  background-color: var(--color-crimson);
  padding: 0.8rem 2rem;
  min-width: 200px;
  transition: all 0.3s ease;
}

.menu-view-button:hover {
  background-color: var(--color-golden);
  color: var(--color-black);
  transform: scale(1.05);
}

/* Container Styles */
.prix-fixe-container,
.main-menu-container,
.brunch-menu-container,
.dessert-menu-container {
  margin: 3rem 0;
  padding: 2rem;
  border-top: 1px solid rgba(220, 202, 135, 0.18);
  border-bottom: 1px solid rgba(220, 202, 135, 0.18);
  background: rgba(15, 40, 70, 0.92);
  border-radius: 4px;
  box-shadow: none;
}

.prix-fixe-title {
  font-family: var(--font-base);
  color: var(--color-golden);
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  letter-spacing: 0.1em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.prix-fixe-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--color-golden), transparent);
}

/* Course Sections */
.menu-course {
  margin-bottom: 3rem;
  position: relative;
  padding: 1rem;
  border-radius: 4px;
  background: rgba(15, 40, 70, 0.85);
  transition: all 0.3s ease;
  border-left: 2px solid rgba(220, 202, 135, 0.18);
  box-shadow: none;
}

.menu-course:hover {
  background: rgba(15, 40, 70, 0.92);
  transform: translateY(-5px);
  border-left: 2px solid rgba(220, 202, 135, 0.4);
}

.course-title {
  font-family: var(--font-base);
  color: var(--color-golden);
  font-size: 1.8rem;
  text-align: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.choice-label {
  text-align: center;
  color: var(--color-grey);
  font-style: italic;
  margin-bottom: 1.5rem;
}

.course-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
}

.course-item {
  text-align: center;
  padding: 1rem;
  background: rgba(15, 40, 70, 0.7);
  border-radius: 4px;
  transition: all 0.3s ease;
  border-left: 1px solid rgba(220, 202, 135, 0.12);
}

.course-item:hover {
  background: rgba(15, 40, 70, 0.85);
  transform: translateX(5px);
  border-left: 1px solid rgba(220, 202, 135, 0.3);
}

.course-item h4 {
  font-family: var(--font-base);
  color: var(--color-white);
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.item-description {
  color: var(--color-grey);
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Menu Categories */
.menu-category {
  margin-bottom: 3rem;
  background: rgba(15, 40, 70, 0.85);
  padding: 1.5rem;
  border-radius: 4px;
  border-left: 2px solid rgba(220, 202, 135, 0.18);
  transition: all 0.3s ease;
  box-shadow: none;
}

.menu-category:hover {
  background: rgba(15, 40, 70, 0.92);
  transform: translateY(-5px);
  border-left: 2px solid rgba(220, 202, 135, 0.4);
}

.category-title {
  font-family: var(--font-base);
  color: var(--color-golden);
  font-size: 1.6rem;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-golden), transparent);
}

.app__menupage-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.75rem;
  transition: all 0.3s ease;
  background: rgba(15, 40, 70, 0.7);
  border-radius: 3px;
  border-bottom: 1px dotted rgba(220, 202, 135, 0.18);
}

.menu-item:hover {
  background: rgba(15, 40, 70, 0.85);
  transform: translateX(5px);
  border-bottom: 1px dotted rgba(220, 202, 135, 0.4);
}

.menu-item-name {
  flex: 1;
}

.menu-item-name h4 {
  font-family: var(--font-base);
  color: var(--color-white);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.menu-item-desc {
  color: var(--color-grey);
  font-size: 0.9rem;
  line-height: 1.4;
}

.menu-item-price {
  font-family: var(--font-base);
  color: var(--color-golden);
  font-size: 1.1rem;
  font-weight: 600;
  white-space: nowrap;
  align-self: flex-start;
}

/* Responsive Styles */
@media screen and (max-width: 1150px) {
  .app__menupage {
    padding: 2rem;
  }
  
  .prix-fixe-title {
    font-size: 2rem;
  }
}

@media screen and (max-width: 850px) {
  .app__menupage-items {
    grid-template-columns: 1fr;
  }
  
  .menu-tab {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .prix-fixe-container,
  .main-menu-container,
  .brunch-menu-container,
  .dessert-menu-container {
    padding: 1.5rem;
  }
}

@media screen and (max-width: 650px) {
  .app__menupage {
    padding: 0.75rem;
    width: 100%;
  }
  
  .menu-container {
    margin: 2rem 0;
    padding: 1.25rem;
    width: 100%;
  }
  
  .menu-category {
    padding: 1rem;
    margin-bottom: 2rem;
    width: 100%;
  }
  
  .app__menupage-items {
    gap: 1rem;
  }
  
  .menu-item {
    width: 100%;
    padding: 0.75rem 0.5rem;
  }
  
  .menu-item-name h4 {
    font-size: 1rem;
  }
  
  .menu-item-desc {
    font-size: 0.85rem;
  }
  
  .menu-item-price {
    font-size: 1rem;
  }
  
  .prix-fixe-title {
    font-size: 1.6rem;
  }
  
  .course-title {
    font-size: 1.5rem;
  }
  
  .menu-tab {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .menu-download-link {
    width: 100%;
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .app__menupage {
    padding: 0.5rem;
  }
  
  .menu-container {
    padding: 1rem;
  }
  
  .menu-tabs {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
  }
  
  .menu-tab {
    width: 100%;
    padding: 0.75rem 0;
  }
  
  .menu-item-name h4 {
    font-size: 0.95rem;
  }
  
  .menu-item-desc {
    font-size: 0.8rem;
  }
  
  .menu-item-price {
    font-size: 0.95rem;
  }
  
  .category-title {
    font-size: 1.4rem;
  }
}

.menu-container {
  margin: 3rem 0;
  padding: 2rem;
  background: rgba(15, 40, 70, 0.92);
  border-radius: 4px;
}

.menu-download {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(220, 202, 135, 0.18);
}

.menu-download-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-golden);
  background-color: rgba(15, 40, 70, 0.85);
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: 1px solid var(--color-golden);
  transition: all 0.3s ease;
  text-decoration: none;
}

.menu-download-link:hover {
  background-color: var(--color-golden);
  color: var(--color-black);
  transform: translateY(-2px);
} 