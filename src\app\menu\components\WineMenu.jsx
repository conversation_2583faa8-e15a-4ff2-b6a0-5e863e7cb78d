import React from 'react';

const WineMenu = () => {
  const redWines = [
    {
      title: "Château Margaux",
      description: "Bordeaux, France | 2015",
      price: "$120"
    },
    {
      title: "Opus One",
      description: "Napa Valley, California | 2018",
      price: "$180"
    },
    {
      title: "<PERSON><PERSON>, <PERSON>",
      description: "Piedmont, Italy | 2016",
      price: "$95"
    },
    {
      title: "Malbec, Catena Zapata",
      description: "Mendoza, Argentina | 2019",
      price: "$65"
    }
  ];

  const whiteWines = [
    {
      title: "Puligny-Montrachet",
      description: "Burgundy, France | 2018",
      price: "$110"
    },
    {
      title: "Cloudy Bay Sauvignon Blanc",
      description: "Marlborough, New Zealand | 2021",
      price: "$65"
    },
    {
      title: "Chardonnay, Kistler",
      description: "Sonoma Coast, California | 2019",
      price: "$85"
    },
    {
      title: "Riesling, Dr. Loosen",
      description: "Mosel, Germany | 2020",
      price: "$55"
    }
  ];

  const sparklingWines = [
    {
      title: "Dom Pérignon",
      description: "Champagne, France | 2012",
      price: "$220"
    },
    {
      title: "Veuve Clicquot",
      description: "Champagne, France | NV",
      price: "$95"
    },
    {
      title: "Franciacorta, Ca' del Bosco",
      description: "Lombardy, Italy | 2016",
      price: "$75"
    },
    {
      title: "Schramsberg Blanc de Blancs",
      description: "Napa Valley, California | 2018",
      price: "$65"
    }
  ];

  return (
    <div className="menu-container">
      <div className="menu-download">
        <a href="/menus/wine-menu.pdf" download className="menu-download-link">
          <i className="fa fa-download"></i> Download Wine Menu PDF
        </a>
      </div>

      <div className="menu-category">
        <h3 className="category-title">Red Wines</h3>
        <div className="app__menupage-items">
          {redWines.map((item, index) => (
            <div className="menu-item" key={`red-wine-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="menu-category">
        <h3 className="category-title">White Wines</h3>
        <div className="app__menupage-items">
          {whiteWines.map((item, index) => (
            <div className="menu-item" key={`white-wine-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="menu-category">
        <h3 className="category-title">Sparkling Wines & Champagne</h3>
        <div className="app__menupage-items">
          {sparklingWines.map((item, index) => (
            <div className="menu-item" key={`sparkling-wine-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WineMenu; 