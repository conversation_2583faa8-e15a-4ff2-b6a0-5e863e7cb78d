"use client";

import React, { useState } from "react";
import { SubHeading, Navbar } from "@/components";
import Image from "next/image";
import Link from "next/link";
import { images } from "@/constants";
import "./Book.css";

const Book = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    date: "",
    time: "",
    guests: "2",
    occasion: "none",
    specialRequests: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the data to your backend
    alert(
      "Reservation request submitted! We'll contact you shortly to confirm."
    );
    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      date: "",
      time: "",
      guests: "2",
      occasion: "none",
      specialRequests: "",
    });
  };

  return (
    <>
      <Navbar />
      <div className="app__book app__bg section__padding">
        <div className="app__book-heading">
          <SubHeading title="Reservations" />
          <h1 className="headtext__cormorant">Book A Table</h1>
          <div className="app__book-back-link">
            <Link href="/" className="p__opensans">
              <span>← Back to Homepage</span>
            </Link>
          </div>
        </div>

        <div className="app__book-container">
          <div className="app__book-form">
            <form onSubmit={handleSubmit}>
              <div className="app__book-form_group">
                <label className="p__cormorant">Your Name*</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter your name"
                  required
                />
              </div>

              <div className="app__book-form_row">
                <div className="app__book-form_group">
                  <label className="p__cormorant">Email*</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email"
                    required
                  />
                </div>
                <div className="app__book-form_group">
                  <label className="p__cormorant">Phone*</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Enter your phone"
                    required
                  />
                </div>
              </div>

              <div className="app__book-form_row">
                <div className="app__book-form_group">
                  <label className="p__cormorant">Date*</label>
                  <input
                    type="date"
                    name="date"
                    value={formData.date}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="app__book-form_group">
                  <label className="p__cormorant">Time*</label>
                  <input
                    type="time"
                    name="time"
                    value={formData.time}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="app__book-form_row">
                <div className="app__book-form_group">
                  <label className="p__cormorant">Number of Guests*</label>
                  <select
                    name="guests"
                    value={formData.guests}
                    onChange={handleChange}
                    required
                  >
                    <option value="1">1 Person</option>
                    <option value="2">2 People</option>
                    <option value="3">3 People</option>
                    <option value="4">4 People</option>
                    <option value="5">5 People</option>
                    <option value="6">6 People</option>
                    <option value="7+">7+ People</option>
                  </select>
                </div>
                <div className="app__book-form_group">
                  <label className="p__cormorant">Occasion</label>
                  <select
                    name="occasion"
                    value={formData.occasion}
                    onChange={handleChange}
                  >
                    <option value="none">None</option>
                    <option value="birthday">Birthday</option>
                    <option value="anniversary">Anniversary</option>
                    <option value="business">Business Meal</option>
                    <option value="special">Special Occasion</option>
                  </select>
                </div>
              </div>

              <div className="app__book-form_group">
                <label className="p__cormorant">Special Requests</label>
                <textarea
                  name="specialRequests"
                  value={formData.specialRequests}
                  onChange={handleChange}
                  placeholder="Any special requests or dietary requirements?"
                  rows="4"
                />
              </div>

              <button type="submit" className="custom__button">
                Confirm Reservation
              </button>
            </form>
          </div>

          <div className="app__book-image">
            <Image
              src={images.findus}
              alt="reservation"
              width={500}
              height={650}
              priority
            />
            <div className="app__book-info">
              <h2
                className="p__cormorant"
                style={{ color: "var(--color-golden)" }}
              >
                Opening Hours
              </h2>
              <p className="p__opensans">Mon - Fri: 10:00 am - 02:00 am</p>
              <p className="p__opensans">Sat - Sun: 10:00 am - 03:00 am</p>
              <div className="app__book-info_divider"></div>
              <h2
                className="p__cormorant"
                style={{ color: "var(--color-golden)" }}
              >
                Contact
              </h2>
              <p className="p__opensans">
                9 W 53rd St, New York, NY 10019, USA
              </p>
              <p className="p__opensans">+1 212-344-1230</p>
              <p className="p__opensans">+1 212-555-1230</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Book;
