# Adding Krylon Regular Font

To complete the font implementation, follow these steps:

1. Download the Krylon Regular font from: https://www.behance.net/gallery/133227745/KRYLON-(free-font)

2. After downloading, place the following font files in this directory:
   - `Krylon-Regular.woff2`
   - `Krylon-Regular.woff`
   - `Krylon-Regular.ttf` (optional, for fallback)

3. The font is already configured in the layout.js file and applied to headings in the CSS.

## Usage

The Krylon font is now set up for use in headings. The following CSS classes already use this font:

- `.headtext__cormorant`
- `.app__header-title`

## Font Information

Krylon is a free font designed by <PERSON>. It's a stylish serif font with over 300 glyphs, ideal for headings and display text.

## Note

If you encounter any issues with the font display, make sure:
1. The font files are correctly placed in this directory
2. The font path in `layout.js` matches the actual location of your font files
3. Clear your browser cache after implementation to ensure the new font is loaded 