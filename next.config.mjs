/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'tatyanaseverydayfood.com',
      },
      {
        protocol: 'https',
        hostname: 'www.alphafoodie.com',
      },
      {
        protocol: 'https',
        hostname: 'thefoodtable.com',
      },
      {
        protocol: 'https',
        hostname: 'foodiewinelover.com',
      },
      {
        protocol: 'https',
        hostname: 'www.pantsdownapronson.com',
      },
      {
        protocol: 'https',
        hostname: 'thegourmetbonvivant.com',
      },
      {
        protocol: 'https',
        hostname: 'scheckeats.com',
      },
    ],
  },
};

export default nextConfig;
