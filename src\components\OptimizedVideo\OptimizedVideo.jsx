"use client";

import React, { useRef, useEffect, useState } from 'react';

/**
 * OptimizedVideo component with built-in performance optimizations based on Next.js documentation
 * 
 * @param {Object} props - Component props
 * @param {string|Object} props.src - Video source string or object with multiple formats
 * @param {boolean} props.autoPlay - Whether to autoplay the video
 * @param {boolean} props.loop - Whether to loop the video
 * @param {boolean} props.muted - Whether to mute the video
 * @param {boolean} props.controls - Whether to show video controls
 * @param {boolean} props.priority - If true, preload video
 * @param {string} props.className - CSS class
 */
const OptimizedVideo = ({
  src,
  autoPlay = false,
  loop = true,
  muted = true,
  controls = false,
  priority = false,
  className = '',
  playsInline = true,
  ...rest
}) => {
  const videoRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const containerRef = useRef(null);
  
  // Check if user is on Android device
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent.toLowerCase();
      setIsAndroid(/android/.test(userAgent));
    }
  }, []);
  
  // Set up intersection observer for lazy loading
  useEffect(() => {
    // If priority, load immediately
    if (priority) {
      setIsVisible(true);
      return;
    }
    
    // If no intersection observer support, load immediately
    if (!containerRef.current || typeof IntersectionObserver === 'undefined') {
      setIsVisible(true);
      return;
    }
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { rootMargin: '200px 0px', threshold: 0.1 }
    );
    
    observer.observe(containerRef.current);
    
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [priority]);
  
  // Handle video loading and autoplay
  useEffect(() => {
    if (!videoRef.current || !isVisible) return;
    
    const videoElement = videoRef.current;
    
    // Load the video
    videoElement.load();
    
    // Handle video loaded state
    const handleLoaded = () => {
      setVideoLoaded(true);
    };
    
    videoElement.addEventListener('loadeddata', handleLoaded);
    
    // If autoplay, attempt to play
    if (autoPlay) {
      // Add a slight delay to ensure video is loaded
      const timer = setTimeout(() => {
        if (videoElement) {
          const playPromise = videoElement.play();
          
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.log("Video play error:", error);
              // Force muted for autoplay (most browsers require this)
              if (videoElement) {
                videoElement.muted = true;
                videoElement.play().catch(innerError => {
                  console.log("Second attempt failed:", innerError);
                });
              }
            });
          }
        }
      }, isAndroid ? 1500 : 1000); // Longer delay for Android
      
      return () => {
        clearTimeout(timer);
        if (videoElement) {
          videoElement.removeEventListener('loadeddata', handleLoaded);
        }
      };
    }
    
    return () => {
      if (videoElement) {
        videoElement.removeEventListener('loadeddata', handleLoaded);
      }
    };
  }, [autoPlay, isVisible, isAndroid]);

  // Determine if src is a string or an object with multiple formats
  const isMultiFormat = typeof src === 'object' && src !== null;

  return (
    <div ref={containerRef} className={`${className} ${videoLoaded ? 'video-loaded' : ''}`} style={{ position: 'relative' }}>
      {isVisible ? (
        <video
          ref={videoRef}
          muted={muted}
          loop={loop}
          controls={controls}
          playsInline={playsInline}
          preload={priority ? 'auto' : 'metadata'}
          webkit-playsinline="true"
          x5-playsinline="true"
          controlsList="nodownload"
          aria-label="Video player"
          {...rest}
        >
          {isMultiFormat ? (
            // Multiple video sources for better compatibility
            Object.entries(src).map(([format, url]) => (
              <source key={format} src={url} type={`video/${format}`} />
            ))
          ) : (
            // Single video source (backwards compatibility)
            <source src={src} type="video/mp4" />
          )}
          Your browser does not support the video tag.
        </video>
      ) : (
        // Placeholder for video while loading
        <div 
          style={{ 
            width: '100%', 
            height: '100%', 
            backgroundColor: '#0C0C0C',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          aria-label="Loading video"
          role="status"
        >
          <div 
            style={{ 
              color: '#DCCA87', 
              textAlign: 'center',
              padding: '20px'
            }}
          >
            Loading video...
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedVideo; 