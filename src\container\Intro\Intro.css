.app__video {
  position: relative;
  overflow: hidden;
  margin: 0;
  height: 500px;
  width: 100%;
  max-width: 100%;
  background: rgba(15, 40, 70, 0.98);
  border-top: 1px solid rgba(220, 202, 135, 0.3);
  border-bottom: 1px solid rgba(220, 202, 135, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.app__video::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(220, 202, 135, 0.5), transparent);
  z-index: 2;
}

.app__video::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(220, 202, 135, 0.5), transparent);
  z-index: 2;
}

.app__video video,
.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.app__video-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background 0.3s ease, opacity 0.5s ease;
  opacity: 0;
}

.app__video-overlay.video-loaded {
  opacity: 1;
}

.app__video-overlay::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, rgba(15, 40, 70, 0.6), transparent);
  z-index: 1;
}

.app__video-overlay:hover {
  background: rgba(0, 0, 0, 0.35);
}

.app__video-overlay_circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 1px solid var(--color-golden);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  background: rgba(15, 40, 70, 0.85);
  box-shadow: 0 0 30px rgba(220, 202, 135, 0.2);
  z-index: 5;
}

.app__video-overlay_circle:hover {
  transform: scale(1.05);
  border-width: 2px;
  background: rgba(15, 40, 70, 0.95);
  box-shadow: 0 0 30px rgba(220, 202, 135, 0.4);
}

@media screen and (min-width: 2000px) {
  .app__video {
    height: 650px;
  }
  
  .app__video-overlay_circle {
    width: 150px;
    height: 150px;
  }
}

@media screen and (max-width: 1150px) {
  .app__video {
    height: 450px;
  }
}

@media screen and (max-width: 850px) {
  .app__video {
    height: 400px;
    margin: 0;
  }
}

@media screen and (max-width: 650px) {
  .app__video {
    height: 350px;
    margin: 0;
  }
  
  .app__video-overlay_circle {
    width: 80px;
    height: 80px;
  }
}

@media screen and (max-width: 450px) {
  .app__video {
    height: 300px;
    margin: 0;
  }
  
  .app__video-overlay_circle {
    width: 65px;
    height: 65px;
  }
}