import React from 'react';

const DessertsMenu = () => {
  const desserts = [
    {
      title: "Chocolate Soufflé",
      description: "Warm chocolate center, vanilla bean ice cream",
      price: "$12"
    },
    {
      title: "Crème Brûlée",
      description: "Vanilla custard, caramelized sugar crust, seasonal berries",
      price: "$10"
    },
    {
      title: "Apple Tarte Tatin",
      description: "Caramelized apples, puff pastry, cinnamon ice cream",
      price: "$11"
    },
    {
      title: "Tiramisu",
      description: "Espresso-soaked ladyfingers, mascarpone cream, cocoa dust",
      price: "$10"
    },
    {
      title: "Lemon Meringue Tart",
      description: "Buttery crust, tangy lemon filling, toasted meringue",
      price: "$9"
    },
    {
      title: "Seasonal Fruit Crisp",
      description: "Chef's selection of seasonal fruits, oat crumble, vanilla ice cream",
      price: "$10"
    }
  ];

  const specialDesserts = [
    {
      title: "Dessert Tasting",
      description: "Chef's selection of three mini desserts",
      price: "$18"
    },
    {
      title: "Cheese Plate",
      description: "Selection of artisanal cheeses, house-made accompaniments",
      price: "$16"
    }
  ];

  const dessertDrinks = [
    {
      title: "Espresso Martini",
      description: "Vodka, coffee liqueur, fresh espresso, simple syrup",
      price: "$14"
    },
    {
      title: "Port Wine",
      description: "Taylor Fladgate 10 Year Tawny",
      price: "$12"
    },
    {
      title: "Sauternes",
      description: "Château Coutet, 2016, Barsac",
      price: "$15"
    }
  ];

  return (
    <div className="menu-container">
      <div className="menu-download">
        <a href="/menus/dessert-menu.pdf" download className="menu-download-link">
          <i className="fa fa-download"></i> Download Desserts Menu PDF
        </a>
      </div>

      <div className="menu-category">
        <h3 className="category-title">Sweet Endings</h3>
        <div className="app__menupage-items">
          {desserts.map((item, index) => (
            <div className="menu-item" key={`dessert-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="menu-category">
        <h3 className="category-title">Dessert Specialties</h3>
        <div className="app__menupage-items">
          {specialDesserts.map((item, index) => (
            <div className="menu-item" key={`special-dessert-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="menu-category">
        <h3 className="category-title">After Dinner Drinks</h3>
        <div className="app__menupage-items">
          {dessertDrinks.map((item, index) => (
            <div className="menu-item" key={`dessert-drink-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DessertsMenu; 