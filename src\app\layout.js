import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Open_Sans, Cormorant } from "next/font/google";
import { Footer } from "@/container";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const openSans = Open_Sans({
  variable: "--font-alt",
  subsets: ["latin"],
});

const cormorant = Cormorant({
  variable: "--font-base",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata = {
  title: "BLUBRASSERIE Restaurant",
  description: "A modern restaurant application",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${openSans.variable} ${cormorant.variable} antialiased`}
      >
        {children}
        <Footer />
      </body>
    </html>
  );
}
