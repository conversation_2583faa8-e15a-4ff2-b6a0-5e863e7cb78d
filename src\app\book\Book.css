.app__book {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

.app__book-heading {
  text-align: center;
  margin-bottom: 3rem;
}

.app__book-back-link {
  margin-top: 1rem;
}

.app__book-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__book-back-link a:hover {
  color: var(--color-white);
}

.app__book-container {
  display: flex;
  justify-content: space-between;
  gap: 4rem;
}

.app__book-form {
  flex: 1;
  background: rgba(15, 40, 70, 0.85);
  padding: 3rem;
  border: 1px solid var(--color-golden);
  border-radius: 4px;
}

.app__book-form form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.app__book-form_group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.app__book-form_row {
  display: flex;
  gap: 2rem;
}

.app__book-form_row .app__book-form_group {
  flex: 1;
}

.app__book-form_group label {
  margin-bottom: 0.5rem;
  color: var(--color-golden);
}

.app__book-form_group input,
.app__book-form_group select,
.app__book-form_group textarea {
  padding: 0.75rem 1rem;
  background: rgba(15, 40, 70, 0.6);
  border: 1px solid var(--color-golden);
  color: var(--color-white);
  font-family: var(--font-alt);
  font-size: 16px;
  width: 100%;
  border-radius: 2px;
  box-sizing: border-box;
}

.app__book-form_group input:focus,
.app__book-form_group select:focus,
.app__book-form_group textarea:focus {
  outline: none;
  border-color: var(--color-golden);
  box-shadow: 0 0 0 2px rgba(220, 202, 135, 0.25);
}

.app__book-form_group input::placeholder,
.app__book-form_group textarea::placeholder {
  color: var(--color-grey);
}

.app__book-form_group input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(0.8) sepia(0.8) saturate(5) hue-rotate(170deg) brightness(0.8);
  cursor: pointer;
}

.app__book-form_group input[type="time"]::-webkit-calendar-picker-indicator {
  filter: invert(0.8) sepia(0.8) saturate(5) hue-rotate(170deg) brightness(0.8);
  cursor: pointer;
}

.app__book-form_group select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23DCCA87' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  cursor: pointer;
}

.app__book-form_buttons {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-top: 1rem;
}

.app__book-form_buttons .custom__button {
  background-color: rgba(218, 179, 38, 0.85);
  color: var(--color-black);
  padding: 0.75rem 2rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  border: 1px solid rgba(220, 202, 135, 0.9);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  font-weight: 700;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
}

.app__book-form_buttons .custom__button:hover {
  background-color: transparent;
  color: var(--color-golden);
}

.app__book-cancel {
  color: var(--color-grey);
  cursor: pointer;
  transition: color 0.3s ease;
  font-family: var(--font-alt);
}

.app__book-cancel:hover {
  color: var(--color-white);
}

.app__book-image {
  flex: 0.8;
  position: relative;
}

.app__book-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.app__book-image:hover img {
  opacity: 0.9;
}

.app__book-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(15, 40, 70, 0.85);
  padding: 2rem;
  border-top: 1px solid var(--color-golden);
}

.app__book-info h2 {
  margin-bottom: 1rem;
}

.app__book-info p {
  margin: 0.5rem 0;
}

.app__book-info_divider {
  width: 80px;
  height: 1px;
  background-color: var(--color-golden);
  margin: 1.5rem 0;
}

/* Responsive Styles */
@media screen and (max-width: 1150px) {
  .app__book-container {
    flex-direction: column;
  }
  
  .app__book-image {
    margin-top: 2rem;
  }
}

@media screen and (max-width: 850px) {
  .app__book-form {
    padding: 2rem;
  }
  
  .app__book-form_buttons {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media screen and (max-width: 650px) {
  .app__book {
    width: 100vw;
    max-width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
  
  .app__book-heading {
    width: 100%;
    padding: 0 0.5rem;
    margin-bottom: 1.5rem;
  }
  
  .app__book-container {
    width: 100%;
    margin: 0;
    padding: 0;
    gap: 2rem;
  }
  
  .app__book-form {
    width: 100%;
    margin: 0;
    padding: 1.5rem 1rem;
    box-sizing: border-box;
  }
  
  .app__book-form form {
    width: 100%;
    padding: 0;
    margin: 0;
  }
  
  .app__book-form_row {
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    margin: 0;
    padding: 0;
  }
  
  .app__book-form_group {
    width: 100%;
    margin: 0;
    padding: 0;
  }
  
  .app__book-form_group input,
  .app__book-form_group select,
  .app__book-form_group textarea {
    width: 100%;
    font-size: 15px;
    box-sizing: border-box;
    padding: 0.8rem;
  }
  
  .custom__button {
    width: 100%;
    justify-content: center;
    text-align: center;
    box-sizing: border-box;
  }
  
  .app__book-image {
    margin-top: 2rem;
    width: 100%;
    padding: 0;
  }
}

@media screen and (max-width: 480px) {
  .app__book-form {
    padding: 1.25rem 0.75rem;
  }
  
  .app__book-heading h1 {
    font-size: 2.5rem;
  }
  
  .app__book-form_group label {
    font-size: 15px;
  }
  
  .app__book-form_group input,
  .app__book-form_group select,
  .app__book-form_group textarea {
    padding: 0.6rem 0.8rem;
    font-size: 14px;
  }
  
  .app__book-info {
    padding: 1.5rem 1rem;
  }
  
  .app__book-info h2 {
    font-size: 1.25rem;
  }
  
  .app__book-info p {
    font-size: 0.9rem;
  }
}

.app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-black);
  width: 100%;
  box-sizing: border-box;
}

.section__padding {
  padding: 4rem 6rem;
}

@media screen and (max-width: 650px) {
  .section__padding {
    padding: 2rem 1rem;
  }
}

@media screen and (max-width: 480px) {
  .section__padding {
    padding: 1.5rem 0.75rem;
  }
} 