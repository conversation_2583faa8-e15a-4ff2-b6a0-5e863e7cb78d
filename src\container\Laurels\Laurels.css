.app__laurels_awards {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 3rem;
  gap: 1.5rem;
}

.app__laurels_awards-card {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  min-width: 230px;
  margin: 0;
  transition: all 0.3s ease;
}

.app__laurels_awards-card:hover {
  transform: scale(1.02);
}

.app__laurels-img {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  width: 100%;
}

.laurels-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
  transition: all 0.3s ease;
  transform: scale(0.9);
}

.app__laurels_awards-card img {
  width: 50px;
  height: 50px;
}

.app__laurels_awards-card_content {
  display: flex;
  flex-direction: column;
  margin-left: 1rem;
}

@media screen and (min-width: 2000px) {
  .app__laurels_awards-card {
    min-width: 390px;
  }
  
  .laurels-image {
    max-height: 900px;
    transform: scale(0.95);
  }
}

@media screen and (max-width: 1150px) {
  .app__laurels-img {
    margin-top: 2rem;
    padding: 1rem 0;
  }
  
  .laurels-image {
    max-width: 70%;
    max-height: 500px;
  }
  
  .app__laurels_awards {
    justify-content: center;
  }
}

@media screen and (max-width: 850px) {
  .app__laurels_awards {
    justify-content: space-around;
  }
  
  .app__laurels_awards-card {
    min-width: 280px;
    margin-bottom: 1.5rem;
  }
  
  .laurels-image {
    max-width: 80%;
    max-height: 450px;
  }
}

@media screen and (max-width: 650px) {
  .app__laurels_awards-card {
    min-width: 100%;
    margin-bottom: 2rem;
  }
  
  .laurels-image {
    max-width: 85%;
    max-height: 380px;
  }
}

@media screen and (max-width: 450px) {
  .app__laurels_awards-card {
    min-width: 100%;
    margin: 1rem 0;
  }
  
  .laurels-image {
    max-width: 75%;
    max-height: 280px;
  }
}
