"use client";

import React, { useState, useEffect } from "react";
import { GiHamburgerMenu } from "react-icons/gi";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";

import { images } from "../../constants";
import MobileMenu from "./MobileMenu";
import "./Navbar.css";

const Navbar = () => {
  const [toggleMenu, setToggleMenu] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();
  const isHomePage = pathname === "/";

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      if (scrollPosition > 100) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Create proper navigation links based on current page
  const getNavLink = (section) => {
    if (isHomePage) {
      return `#${section}`;
    } else {
      return `/#${section}`;
    }
  };

  return (
    <nav className={`app__navbar ${scrolled ? 'scrolled' : ''}`}>
      <div className="app__navbar-logo">
        <Link href="/">
          <Image 
            src="/assets/BLUBRASSERIE.svg" 
            alt="BLUBRASSERIE logo" 
            width={120}
            height={35}
            priority
          />
        </Link>
      </div>
      <ul className="app__navbar-links">
        <li className="p__opensans">
          <Link href={getNavLink("menu")}>Menu</Link>
        </li>
        <li className="p__opensans">
          <Link href={getNavLink("about")}>About</Link>
        </li>
        <li className="p__opensans">
          <Link href={getNavLink("events")}>Events</Link>
        </li>
        <li className="p__opensans">
          <Link href={getNavLink("contact")}>Hours & Info</Link>
        </li>
      </ul>
      <div className="app__navbar-actions">
        <Link href="/private-events" className="private-events-button">
          <span className="p__opensans">Private Events</span>
        </Link>
        <Link href="/book" className="book-button">
          <span className="p__opensans">Reserve Table</span>
        </Link>
      </div>
      <div className="app__navbar-smallscreen">
        <GiHamburgerMenu
          color="#fff"
          fontSize={27}
          className="hamburgerMenu"
          onClick={() => setToggleMenu(true)}
        />
        <MobileMenu isOpen={toggleMenu} setIsOpen={setToggleMenu} pathname={pathname} />
      </div>
    </nav>
  );
};

export default Navbar;
