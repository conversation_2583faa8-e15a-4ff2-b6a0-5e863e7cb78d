"use client";

import React, { useState, useRef, useEffect, Suspense } from "react";
import { BsFillPlayFill, BsPauseFill } from "react-icons/bs";

import OptimizedVideo from "../../components/OptimizedVideo/OptimizedVideo";
import { mealFormats } from "../../constants";
import "./Intro.css";

const VideoSkeleton = () => (
  <div 
    className="video-skeleton"
    style={{
      width: '100%',
      height: '100%',
      backgroundColor: '#0C0C0C',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}
    aria-label="Loading video"
    role="status"
  >
    <div 
      style={{ 
        color: '#DCCA87', 
        textAlign: 'center',
        padding: '20px'
      }}
    >
      Loading video...
    </div>
  </div>
);

const IntroVideo = ({ videoRef, onLoadedData, isMobile, muted }) => (
  <OptimizedVideo
    src={mealFormats}
    loop={true}
    muted={muted}
    playsInline={true}
    autoPlay={false}
    className="video-element"
    priority={false}
    ref={videoRef}
    onLoadedData={onLoadedData}
  />
);

const Intro = () => {
  const [playVideo, setPlayVideo] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef(null);

  // Check if user is on mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Set on initial load
    checkMobile();
    
    // Add event listener for resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Add handler for video loading events
  const handleVideoLoaded = () => {
    setVideoLoaded(true);
  };

  useEffect(() => {
    // Control video playback when playVideo state changes
    if (videoRef.current) {
      if (playVideo) {
        const playPromise = videoRef.current.play();
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.error("Video play error:", error);
            // Auto-fallback to muted playback if needed
            videoRef.current.muted = true;
            videoRef.current.play().catch(e => console.error("Fallback play failed:", e));
          });
        }
      } else {
        if (videoRef.current.pause) {
          videoRef.current.pause();
        }
      }
    }
  }, [playVideo]);

  const handlePlayPause = () => {
    setPlayVideo(!playVideo);
    
    // Auto-pause video after 2 minutes on mobile to save data
    if (!playVideo && isMobile) {
      setTimeout(() => {
        setPlayVideo(false);
      }, 120000); // 2 minutes
    }
  };

  return (
    <div className="app__video">
      <Suspense fallback={<VideoSkeleton />}>
        <IntroVideo 
          videoRef={videoRef} 
          onLoadedData={handleVideoLoaded} 
          isMobile={isMobile} 
          muted={true}
        />
      </Suspense>
      <div className={`app__video-overlay flex__center ${videoLoaded ? 'video-loaded' : ''}`}>
        <div
          className="app__video-overlay_circle flex__center"
          onClick={handlePlayPause}
          aria-label={playVideo ? "Pause video" : "Play video"}
          role="button"
          tabIndex="0"
        >
          {playVideo ? (
            <BsPauseFill color="#fff" fontSize={isMobile ? 24 : 30} />
          ) : (
            <BsFillPlayFill color="#fff" fontSize={isMobile ? 24 : 30} />
          )}
        </div>
      </div>
    </div>
  );
};

export default Intro;
