"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";

import images from "../../constants/images";
import "./AboutUs.css";

const AboutUs = () => (
  <div
    className="app__aboutus app__bg flex__center section__padding"
    id="about"
  >
    <div className="app__aboutus-overlay flex__center">
      <Image 
        src={images.BLUBRASSERIE} 
        alt="BLUBRASSERIE_overlay" 
        width={391} 
        height={415}
        style={{ opacity: 0.4 }} 
      />
    </div>

    <div className="app__aboutus-content">
      <div className="app__aboutus-row">
        <div className="app__aboutus-text">
          <h1 className="headtext__cormorant">About Us</h1>
          <Image src={images.spoon} alt="about_spoon" width={45} height={15} className="spoon__img" />
          <p className="p__opensans">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quis pharetra
            adipiscing ultrices vulputate posuere tristique. In sed odio nec
            aliquet eu proin mauris et.
          </p>
          <Link href="/about">
            <button type="button" className="custom__button">
              Know More
            </button>
          </Link>
        </div>
        <div className="app__aboutus-img">
          <Image src={images.welcome} alt="about_image" width={500} height={400} />
        </div>
      </div>

      <div className="app__aboutus-row">
        <div className="app__aboutus-img">
          <Image 
            src={images.domaineRamonet} 
            alt="wine_image" 
            width={400} 
            height={500} 
            style={{ objectFit: 'contain' }}
          />
        </div>
        <div className="app__aboutus-text">
          <h1 className="headtext__cormorant">Our History</h1>
          <Image src={images.spoon} alt="about_spoon" width={45} height={15} className="spoon__img" />
          <p className="p__opensans">
            Adipiscing tempus ullamcorper lobortis odio tellus arcu volutpat.
            Risus placerat morbi volutpat habitasse interdum mi aliquam In sed
            odio nec aliquet.
          </p>
          <Link href="/about">
            <button type="button" className="custom__button">
              Know More
            </button>
          </Link>
        </div>
      </div>
    </div>
  </div>
);

export default AboutUs;
