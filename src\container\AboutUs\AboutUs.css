.app__aboutus {
  position: relative;
}

.app__aboutus-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.app__aboutus-overlay img {
  width: 391px;
  height: 415px;
  z-index: 0;
  opacity: 0.4;
}

.app__aboutus-content {
  width: 100%;
  z-index: 2;
}

.app__aboutus-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rem;
}

.app__aboutus-row.reverse {
  flex-direction: row-reverse;
}

.app__aboutus-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 2rem;
}

.app__aboutus-row .app__aboutus-text {
  align-items: flex-end;
  text-align: right;
}

.app__aboutus-row.reverse .app__aboutus-text {
  align-items: flex-start;
  text-align: left;
}

.app__aboutus-img {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.app__aboutus-img img {
  width: 100%;
  max-width: 500px;
  height: auto;
  object-fit: cover;
  border-radius: 8px;
}

.app__aboutus-text p {
  margin: 2rem 0;
  color: var(--color-grey);
}

@media screen and (min-width: 2000px) {
  .app__aboutus-text p {
    margin: 4rem 0;
  }
  
  .app__aboutus-img img {
    max-width: 650px;
  }
}

@media screen and (max-width: 900px) {
  .app__aboutus-row,
  .app__aboutus-row.reverse {
    flex-direction: column;
    margin-bottom: 2rem;
  }
  
  .app__aboutus-text {
    margin-bottom: 2rem;
    align-items: center;
    text-align: center;
  }
  
  .app__aboutus-row .app__aboutus-text,
  .app__aboutus-row.reverse .app__aboutus-text {
    align-items: center;
    text-align: center;
  }
}

@media screen and (max-width: 650px) {
  .app__aboutus-overlay img {
    width: 80%;
    height: 320px;
    opacity: 0.4;
  }
  
  .app__aboutus-img img {
    max-width: 100%;
  }
}
