import React from 'react';
import Link from 'next/link';

const DinnerMenu = () => {
  const dinnerItems = [
    {
      title: "Filet Mignon",
      description: "Prime beef, truffle butter, seasonal vegetables",
      price: "$48"
    },
    {
      title: "Salmon",
      description: "Grilled Atlantic salmon, lemon beurre blanc, asparagus",
      price: "$36"
    },
    {
      title: "Rack of Lamb",
      description: "Herb crusted, mint jus, roasted potatoes",
      price: "$45"
    },
    {
      title: "Duck a l'Orange",
      description: "Roasted duck breast, orange sauce, wild rice",
      price: "$38"
    },
    {
      title: "Seafood Risotto",
      description: "Arborio rice, shrimp, scallops, saffron",
      price: "$36"
    },
    {
      title: "Vegetable Wellington",
      description: "Seasonal vegetables, puff pastry, red pepper coulis",
      price: "$32"
    }
  ];

  const appetizers = [
    {
      title: "Truffle Arancini",
      description: "Crispy risotto balls, black truffle, parmesan fondue",
      price: "$14"
    },
    {
      title: "Tuna Tartare",
      description: "Sushi grade tuna, avocado, cucumber, citrus ponzu, wonton crisps",
      price: "$18"
    },
    {
      title: "Burrata",
      description: "Heirloom tomatoes, basil oil, aged balsamic, grilled sourdough",
      price: "$16"
    },
    {
      title: "Beef Carpaccio",
      description: "Thinly sliced beef tenderloin, arugula, capers, parmesan, truffle aioli",
      price: "$17"
    }
  ];

  return (
    <div className="menu-container">
      <div className="menu-download">
        <a href="/menus/dinner-menu.pdf" download className="menu-download-link">
          <i className="fa fa-download"></i> Download Dinner Menu PDF
        </a>
      </div>

      <div className="menu-category">
        <h3 className="category-title">Appetizers</h3>
        <div className="app__menupage-items">
          {appetizers.map((item, index) => (
            <div className="menu-item" key={`appetizer-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="menu-category">
        <h3 className="category-title">Main Courses</h3>
        <div className="app__menupage-items">
          {dinnerItems.map((item, index) => (
            <div className="menu-item" key={`dinner-${index}`}>
              <div className="menu-item-name">
                <h4>{item.title}</h4>
                <p className="menu-item-desc">{item.description}</p>
              </div>
              <div className="menu-item-price">{item.price}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DinnerMenu; 