"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { SubHeading, Navbar } from "@/components";
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaInstagram, FaFacebookF, FaTwitter } from "react-icons/fa";
import { images } from "@/constants";
import "./Contact.css";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would normally send the form data to your backend
    console.log("Form submitted:", formData);
    setFormSubmitted(true);

    // Reset form after submission
    setTimeout(() => {
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      setFormSubmitted(false);
    }, 3000);
  };

  // Contact sections data for the modern card layout
  const contactSections = [
    {
      id: 1,
      subtitle: "Visit Us",
      title: "Restaurant Location",
      image: images.gallery01,
      description: "Located in the heart of the culinary district, our restaurant offers an elegant dining experience with exceptional cuisine and warm hospitality. Join us for an unforgettable meal in our beautifully designed space.",
      details: [
        { icon: FaMapMarkerAlt, label: "Address", value: "123 Gourmet Avenue, Culinary District, NY 10001" },
        { icon: FaClock, label: "Hours", value: "Mon-Fri: 12pm - 10pm, Sat-Sun: 11am - 11pm" }
      ],
      cta: "Get Directions"
    },
    {
      id: 2,
      subtitle: "Contact Us",
      title: "Get In Touch",
      image: images.gallery02,
      description: "We're here to help make your dining experience exceptional. Whether you have questions about our menu, want to make a reservation, or need assistance with special requests, our team is ready to assist you.",
      details: [
        { icon: FaPhone, label: "Phone", value: "(*************" },
        { icon: FaEnvelope, label: "Email", value: "<EMAIL>" }
      ],
      cta: "Call Now"
    },
    {
      id: 3,
      subtitle: "Connect",
      title: "Send a Message",
      image: images.gallery03,
      description: "Have a special request or feedback to share? We'd love to hear from you. Send us a message and we'll get back to you promptly. Your satisfaction is our priority and we value your communication.",
      details: [
        { icon: FaInstagram, label: "Instagram", value: "@restaurant" },
        { icon: FaFacebookF, label: "Facebook", value: "/restaurant" }
      ],
      cta: "Send Message"
    }
  ];

  return (
    <>
      <Navbar />
      <div className="app__contactpage">
        <div className="app__contactpage-heading">
          <SubHeading title="Get In Touch" />
          <h1 className="headtext__cormorant">Contact Us</h1>
          <div className="app__contactpage-back-link">
            <Link href="/" className="p__opensans">
              <span>← Back To Homepage</span>
            </Link>
          </div>
        </div>

        {/* Modern Contact Showcase Sections */}
        <div className="app__contact-showcase">
          <div className="contact-showcase-container">
            {contactSections.map((section, index) => (
              <div key={section.id} className={`contact-showcase ${index % 2 === 1 ? 'contact-showcase--reverse' : ''}`}>
                <div className="contact-showcase_image">
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    style={{ objectFit: 'cover' }}
                    quality={95}
                  />
                  <div className="contact-showcase_image-overlay"></div>
                </div>

                <div className="contact-showcase_content">
                  <div className="contact-showcase_text">
                    <div className="contact-subtitle">{section.subtitle}</div>
                    <h2 className="contact-title">{section.title}</h2>
                    <p className="contact-description">{section.description}</p>

                    <div className="contact-details">
                      {section.details.map((detail, detailIndex) => (
                        <div key={detailIndex} className="contact-detail-item">
                          <detail.icon className="contact-detail-icon" />
                          <div className="contact-detail-content">
                            <span className="contact-detail-label">{detail.label}:</span>
                            <span className="contact-detail-value">{detail.value}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    <button className="contact-cta">
                      <span>{section.cta}</span>
                      <div className="cta-arrow">→</div>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Form Section */}
        <div className="app__contact-form-section">
          <div className="contact-form-showcase">
            <div className="contact-form-showcase_image">
              <Image
                src={images.gallery04}
                alt="Contact us"
                fill
                style={{ objectFit: 'cover' }}
                quality={95}
              />
              <div className="contact-form-showcase_image-overlay"></div>
            </div>

            <div className="contact-form-showcase_content">
              <div className="contact-form-showcase_text">
                <div className="contact-subtitle">Message Us</div>
                <h2 className="contact-title">Send Us a Message</h2>
                <p className="contact-description">
                  Have a question, special request, or feedback? We'd love to hear from you.
                  Fill out the form below and our team will get back to you promptly.
                </p>

                {formSubmitted ? (
                  <div className="form-success">
                    <p className="p__opensans">Thank you for your message! We&apos;ll get back to you soon.</p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="modern-contact-form">
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="name">Name</label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          placeholder="Your Name"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="email">Email</label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          placeholder="Your Email"
                        />
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="subject">Subject</label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        required
                        placeholder="Subject"
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="message">Message</label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        required
                        placeholder="Your Message"
                        rows="5"
                      ></textarea>
                    </div>

                    <button type="submit" className="contact-cta">
                      <span>Send Message</span>
                      <div className="cta-arrow">→</div>
                    </button>
                  </form>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Quote Section */}
        <div className="app__contactpage-quote">
          <div className="quote-container">
            <div className="quote-icon">
              <Image
                src={images.spoon}
                alt="spoon"
                width={45}
                height={15}
              />
            </div>
            <p className="p__opensans">
              &quot;We believe in creating memorable dining experiences. If you have any questions, feedback, or would like to make a special request, please don&apos;t hesitate to contact us.&quot;
            </p>
            <p className="p__cormorant">— Chef Kevin Luo</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactPage; 