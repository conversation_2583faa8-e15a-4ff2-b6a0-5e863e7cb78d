"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { SubHeading, Navbar } from "@/components";
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaInstagram, FaFacebookF, FaTwitter, FaDirections } from "react-icons/fa";
import { images } from "@/constants";
import "./Contact.css";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would normally send the form data to your backend
    console.log("Form submitted:", formData);
    setFormSubmitted(true);

    // Reset form after submission
    setTimeout(() => {
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      setFormSubmitted(false);
    }, 3000);
  };



  return (
    <>
      <Navbar />
      <div className="app__contactpage">
        {/* Header Section */}
        <div className="contact-header">
          <h1 className="contact-main-title">Contact Us</h1>
          <p className="contact-main-description">
            Welcome to our restaurant, where we create exceptional dining experiences with carefully crafted dishes
            and warm hospitality. We'd love to hear from you and assist with any inquiries or reservations.
          </p>
        </div>

        {/* Main Contact Content */}
        <div className="contact-main-content">
          {/* Contact Form */}
          <div className="contact-form-section">
            {formSubmitted ? (
              <div className="form-success">
                <p>Thank you for your message! We&apos;ll get back to you soon.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-group">
                  <label htmlFor="name">First Name *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Input your first name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">Your Email *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="Input your last name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="message">Your Message *</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    placeholder="Input your last name"
                    rows="4"
                  ></textarea>
                </div>

                <button type="submit" className="submit-btn">
                  Submit
                </button>
              </form>
            )}
          </div>

          {/* Working Hours */}
          <div className="working-hours-section">
            <div className="working-hours-card">
              <h2>Working Hours</h2>
              <div className="hours-item">
                <span className="day">Monday-Friday:</span>
                <span className="time">08:00 am -12:00 am</span>
              </div>
              <div className="hours-item">
                <span className="day">Saturday-Sunday:</span>
                <span className="time">07:00am -11:00 pm</span>
              </div>
            </div>
          </div>

          {/* Google Maps Integration */}
          <div className="contact-map-column">
            <div className="map-frame-compact">
              <div className="map-golden-border-compact">
                <div className="map-inner-frame-compact">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.9663095343008!2d-74.00425878459418!3d40.74844097932681!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c259bf5c1654f3%3A0xc80f9cfce5383d5d!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sus!4v1635959542208!5m2!1sen!2sus"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen=""
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Restaurant Location"
                  ></iframe>
                </div>
              </div>

              {/* Compact decorative corners */}
              <div className="map-corner-compact map-corner-compact--top-left"></div>
              <div className="map-corner-compact map-corner-compact--top-right"></div>
              <div className="map-corner-compact map-corner-compact--bottom-left"></div>
              <div className="map-corner-compact map-corner-compact--bottom-right"></div>
            </div>
          </div>
        </div>


      </div>
    </>
  );
};

export default ContactPage; 