"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { SubHeading, Navbar } from "@/components";
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaInstagram, FaFacebookF, FaTwitter } from "react-icons/fa";
import { images } from "@/constants";
import "./Contact.css";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would normally send the form data to your backend
    console.log("Form submitted:", formData);
    setFormSubmitted(true);

    // Reset form after submission
    setTimeout(() => {
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      setFormSubmitted(false);
    }, 3000);
  };



  return (
    <>
      <Navbar />
      <div className="app__contactpage">
        {/* Header Section */}
        <div className="contact-header">
          <h1 className="contact-main-title">Contact Us</h1>
          <p className="contact-main-description">
            Welcome to Burger Bliss, where we take your cravings to a whole new level! Our mouthwatering
            burgers are made from 100% beef and are served on freshly baked buns.
          </p>
        </div>

        {/* Main Contact Content */}
        <div className="contact-main-content">
          {/* Contact Form */}
          <div className="contact-form-section">
            {formSubmitted ? (
              <div className="form-success">
                <p>Thank you for your message! We&apos;ll get back to you soon.</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-group">
                  <label htmlFor="name">First Name *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Input your first name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">Your Email *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="Input your last name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="message">Your Message *</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    placeholder="Input your last name"
                    rows="4"
                  ></textarea>
                </div>

                <button type="submit" className="submit-btn">
                  Submit
                </button>
              </form>
            )}
          </div>

          {/* Working Hours */}
          <div className="working-hours-section">
            <div className="working-hours-card">
              <h2>Working Hours</h2>
              <div className="hours-item">
                <span className="day">Monday-Friday:</span>
                <span className="time">08:00 am -12:00 am</span>
              </div>
              <div className="hours-item">
                <span className="day">Saturday-Sunday:</span>
                <span className="time">07:00am -11:00 pm</span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="contact-info-section">
            <div className="contact-info-item">
              <h3>Contact</h3>
              <p>+62 361 9002440</p>
              <p><EMAIL></p>
            </div>

            <div className="contact-info-item">
              <h3>Restaurant</h3>
              <p>12 Rue de la Fontaine</p>
              <p>75001 Paris, France</p>
            </div>

            <div className="contact-info-item">
              <h3>Office</h3>
              <p>45 Rue du Faubourg</p>
              <p>31000 Toulouse, France</p>
            </div>

            <div className="contact-info-item">
              <h3>Social</h3>
              <p>Instagram</p>
              <p>Facebook</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactPage; 