"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import Image from "next/image";
import { SubHeading, Navbar } from "@/components";
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaInstagram, FaFacebookF, FaTwitter } from "react-icons/fa";
import { images } from "@/constants";
import "./Contact.css";

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would normally send the form data to your backend
    console.log("Form submitted:", formData);
    setFormSubmitted(true);
    
    // Reset form after submission
    setTimeout(() => {
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      setFormSubmitted(false);
    }, 3000);
  };

  return (
    <>
      <Navbar />
      <div className="app__contactpage app__bg">
        <div className="app__contactpage-heading">
          <SubHeading title="Get In Touch" />
          <h1 className="headtext__cormorant">Contact Us</h1>
          <div className="app__contactpage-back-link">
            <Link href="/" className="p__opensans">
              <span>← Back To Homepage</span>
            </Link>
          </div>
        </div>

        <div className="app__contactpage-container">
          <div className="app__contactpage-info">
            <div className="contact-card">
              <h2 className="p__cormorant">Restaurant Information</h2>
              
              <div className="contact-info-item">
                <FaMapMarkerAlt className="contact-icon" />
                <div>
                  <h3>Address</h3>
                  <p className="p__opensans">123 Gourmet Avenue, Culinary District, NY 10001</p>
                </div>
              </div>
              
              <div className="contact-info-item">
                <FaPhone className="contact-icon" />
                <div>
                  <h3>Phone</h3>
                  <p className="p__opensans">(*************</p>
                </div>
              </div>
              
              <div className="contact-info-item">
                <FaEnvelope className="contact-icon" />
                <div>
                  <h3>Email</h3>
                  <p className="p__opensans"><EMAIL></p>
                </div>
              </div>
              
              <div className="contact-info-item">
                <FaClock className="contact-icon" />
                <div>
                  <h3>Hours</h3>
                  <p className="p__opensans">Mon-Fri: 12pm - 10pm</p>
                  <p className="p__opensans">Sat-Sun: 11am - 11pm</p>
                </div>
              </div>
              
              <div className="contact-social">
                <h3>Follow Us</h3>
                <div className="contact-social-icons">
                  <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                    <FaInstagram className="social-icon" />
                  </a>
                  <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                    <FaFacebookF className="social-icon" />
                  </a>
                  <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                    <FaTwitter className="social-icon" />
                  </a>
                </div>
              </div>
            </div>
            
            <div className="contact-map">
              <Image
                src={images.findus}
                alt="Restaurant location"
                width={400}
                height={300}
                className="map-image"
              />
              <div className="map-overlay">
                <Link href="https://maps.google.com" target="_blank" rel="noopener noreferrer">
                  <button type="button" className="custom__button">View on Google Maps</button>
                </Link>
              </div>
            </div>
          </div>
          
          <div className="app__contactpage-form">
            <div className="contact-form-container">
              <h2 className="p__cormorant">Send Us a Message</h2>
              
              {formSubmitted ? (
                <div className="form-success">
                  <p className="p__opensans">Thank you for your message! We&apos;ll get back to you soon.</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="form-group">
                    <label htmlFor="name">Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      placeholder="Your Name"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="email">Email</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      placeholder="Your Email"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="subject">Subject</label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      placeholder="Subject"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="message">Message</label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      placeholder="Your Message"
                      rows="5"
                    ></textarea>
                  </div>
                  
                  <button type="submit" className="custom__button">Send Message</button>
                </form>
              )}
            </div>
          </div>
        </div>
        
        <div className="app__contactpage-quote">
          <div className="quote-container">
            <div className="quote-icon">
              <Image 
                src={images.spoon} 
                alt="spoon" 
                width={45}
                height={15}
              />
            </div>
            <p className="p__opensans">
              &quot;We believe in creating memorable dining experiences. If you have any questions, feedback, or would like to make a special request, please don&apos;t hesitate to contact us.&quot;
            </p>
            <p className="p__cormorant">— Chef Kevin Luo</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactPage; 