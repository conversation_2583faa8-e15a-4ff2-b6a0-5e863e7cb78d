"use client";

import React from "react";
import { SubHeading, Navbar } from "@/components";
import Image from "next/image";
import Link from "next/link";
import { images } from "@/constants";
import "./About.css";

const AboutPage = () => {
  return (
    <>
      <Navbar />
      <div className="app__aboutpage app__bg section__padding">
        <div className="app__aboutpage-heading">
          <SubHeading title="Our Story" />
          <h1 className="headtext__cormorant">About Us</h1>
          <div className="app__aboutpage-back-link">
            <Link href="/" className="p__opensans">
              <span>← Back to Homepage</span>
            </Link>
          </div>
        </div>

        <div className="app__aboutpage-content">
          <div className="app__aboutpage-history">
            <div className="app__aboutpage-history_image">
              <Image 
                src={images.G} 
                alt="G letter" 
                width={300}
                height={320}
                className="app__aboutpage-history_overlay"
              />
              <Image 
                src={images.findus} 
                alt="Restaurant interior" 
                width={500}
                height={650}
                priority
              />
            </div>
            <div className="app__aboutpage-history_text">
              <h2 className="headtext__cormorant">Our Journey</h2>
              <div className="app__aboutpage-history_spoon">
                <Image src={images.spoon} alt="spoon" width={45} height={15} />
              </div>
              <p className="p__opensans">
                Founded in 2018, BLUBRASSERIE emerged from a passion for exceptional dining and culinary artistry. What began as a small family restaurant has evolved into one of the city&apos;s most celebrated fine dining establishments, honored with multiple awards for our innovative approach to modern cuisine with traditional roots.
              </p>
              <p className="p__opensans">
                Our journey has been guided by a commitment to authenticity, quality, and creating memorable experiences for our guests. Over the years, we&apos;ve refined our craft while staying true to our founding principles—sourcing the finest ingredients, honoring culinary traditions, and delivering impeccable service in an atmosphere of sophisticated comfort.
              </p>
              <p className="p__opensans">
                Each dish at BLUBRASSERIE tells a story—of heritage, of innovation, and of our continuous pursuit of culinary excellence. As we look to the future, we remain dedicated to pushing boundaries while honoring the timeless art of hospitality that has defined us from the beginning.
              </p>
            </div>
          </div>

          <div className="app__aboutpage-values">
            <h2 className="headtext__cormorant">Our Philosophy</h2>
            <div className="app__aboutpage-values_spoon">
              <Image src={images.spoon} alt="spoon" width={45} height={15} />
            </div>
            <div className="app__aboutpage-values_cards">
              <div className="app__aboutpage-values_card">
                <h3 className="p__cormorant">Exceptional Ingredients</h3>
                <p className="p__opensans">
                  We source only the finest seasonal ingredients from local farmers and premium suppliers around the world, ensuring each dish represents the peak of flavor and quality.
                </p>
              </div>
              <div className="app__aboutpage-values_card">
                <h3 className="p__cormorant">Culinary Artistry</h3>
                <p className="p__opensans">
                  Our expert chefs blend traditional techniques with innovative approaches, creating dishes that are both familiar and surprising—a true celebration of culinary creativity.
                </p>
              </div>
              <div className="app__aboutpage-values_card">
                <h3 className="p__cormorant">Elegant Ambiance</h3>
                <p className="p__opensans">
                  Every element of our space has been thoughtfully designed to create an atmosphere of refined luxury that complements our culinary offerings and enhances your dining experience.
                </p>
              </div>
              <div className="app__aboutpage-values_card">
                <h3 className="p__cormorant">Impeccable Service</h3>
                <p className="p__opensans">
                  Our dedicated team is committed to anticipating your needs and providing attentive, personalized service that makes every visit to BLUBRASSERIE truly memorable.
                </p>
              </div>
            </div>
          </div>

          <div className="app__aboutpage-team">
            <h2 className="headtext__cormorant">Meet Our Team</h2>
            <div className="app__aboutpage-team_spoon">
              <Image src={images.spoon} alt="spoon" width={45} height={15} />
            </div>
            <div className="app__aboutpage-team_content">
              <div className="app__aboutpage-team_image">
                <Image 
                  src={images.chef} 
                  alt="Our Chef" 
                  width={600}
                  height={600}
                />
              </div>
              <div className="app__aboutpage-team_text">
                <h3 className="p__cormorant">Kevin Luo</h3>
                <h4 className="p__cormorant" style={{ color: 'var(--color-golden)' }}>Executive Chef & Founder</h4>
                <p className="p__opensans">
                  With over 20 years of culinary experience in renowned restaurants across Europe and Asia, Chef Kevin Luo brings a unique perspective to BLUBRASSERIE&apos;s kitchen. His innovative approach combines classical training with a deep appreciation for global flavors and techniques.
                </p>
                <p className="p__opensans">
                  Chef Luo&apos;s philosophy centers on respecting ingredients, honoring traditions, and creating dishes that evoke emotion. Under his leadership, our culinary team continues to push boundaries while maintaining the highest standards of excellence.
                </p>
                <p className="p__opensans">
                  &quot;Cooking is about passion, about creating memories through flavors. At BLUBRASSERIE, we don&apos;t just serve food—we craft experiences that linger long after the last bite.&quot;
                </p>
                <p className="p__opensans" style={{ fontStyle: 'italic', color: 'var(--color-grey)' }}>— Kevin Luo</p>
              </div>
            </div>
          </div>

          <div className="app__aboutpage-visit">
            <h2 className="headtext__cormorant">Visit Us</h2>
            <div className="app__aboutpage-visit_spoon">
              <Image src={images.spoon} alt="spoon" width={45} height={15} />
            </div>
            <div className="app__aboutpage-visit_content">
              <div className="app__aboutpage-visit_info">
                <h3 className="p__cormorant">Hours</h3>
                <p className="p__opensans">Mon - Fri: 10:00 am - 02:00 am</p>
                <p className="p__opensans">Sat - Sun: 10:00 am - 03:00 am</p>
                
                <h3 className="p__cormorant">Location</h3>
                <p className="p__opensans">9 W 53rd St, New York, NY 10019, USA</p>
                
                <h3 className="p__cormorant">Contact</h3>
                <p className="p__opensans">+1 212-344-1230</p>
                <p className="p__opensans">+1 212-555-1230</p>
                <p className="p__opensans"><EMAIL></p>
              </div>
              <div className="app__aboutpage-visit_buttons">
                <Link href="/book">
                  <button type="button" className="custom__button">
                    Book A Table
                  </button>
                </Link>
                <Link href="/menu">
                  <button type="button" className="custom__button">
                    View Full Menu
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AboutPage; 