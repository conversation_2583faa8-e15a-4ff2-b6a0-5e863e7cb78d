import React from "react";
import Link from "next/link";
import Image from "next/image";

import { SubHeading, MenuItem } from "../../components";
import { data, images } from "../../constants";
import "./SpecialMenu.css";

const SpecialMenu = () => (
  <div className="app__specialMenu flex__center section__padding" id="menu" style={{ paddingBottom: '4rem' }}>
    <div className="app__specialMenu-title">
      <SubHeading title="Menu that fits your palatte" />
      <h1 className="headtext__cormorant">Today&apos;s Special</h1>
      <p className="p__opensans app__specialMenu-title_description">
        Explore our carefully curated menu featuring today's highlights. For our complete offerings, check out our full menu.
      </p>
    </div>

    <div className="app__specialMenu-menu">
      <div className="app__specialMenu-menu_wine flex__center">
        <div className="app__specialMenu-menu_heading-container">
          <p className="app__specialMenu-menu_heading">Wine & Beer</p>
          <div className="app__specialMenu-menu_heading-line"></div>
        </div>
        <div className="app__specialMenu_menu_items">
          {data.wines.map((wine, index) => (
            <MenuItem
              key={wine.title + index}
              title={wine.title}
              price={wine.price}
              tags={wine.tags}
            />
          ))}
        </div>
      </div>

      <div className="app__specialMenu-menu_img">
        <Image 
          src={images.chateauLatour} 
          alt="wine__img" 
          width={280}
          height={500}
          priority
          className="app__specialMenu-menu_img-inner"
        />
        <div className="app__specialMenu-menu_img-overlay">
          <Link href="/menu">
            <button type="button" className="custom__button floating-button">
              View Full Menu
            </button>
          </Link>
        </div>
      </div>

      <div className="app__specialMenu-menu_cocktails flex__center">
        <div className="app__specialMenu-menu_heading-container">
          <p className="app__specialMenu-menu_heading">Cocktails</p>
          <div className="app__specialMenu-menu_heading-line"></div>
        </div>
        <div className="app__specialMenu_menu_items">
          {data.cocktails.map((cocktail, index) => (
            <MenuItem
              key={cocktail.title + index}
              title={cocktail.title}
              price={cocktail.price}
              tags={cocktail.tags}
            />
          ))}
        </div>
      </div>
    </div>
    
    <div className="app__specialMenu-chef-note">
      <div className="app__specialMenu-chef-note_icon">
        <Image 
          src={images.spoon} 
          alt="spoon" 
          width={45}
          height={15}
        />
      </div>
      <p className="p__opensans">
        "Our menu changes seasonally to showcase the freshest ingredients and innovative flavor combinations."
      </p>
      <p className="p__cormorant">— Chef Kevin Luo</p>
    </div>
  </div>
);

export default SpecialMenu;
