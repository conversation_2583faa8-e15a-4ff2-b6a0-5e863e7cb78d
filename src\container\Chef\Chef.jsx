"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { SubHeading } from "../../components";
import { BsArrowLeftShort, BsArrowRightShort } from "react-icons/bs";
import "./Chef.css";
import Link from "next/link";

const Chef = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchPosition, setTouchPosition] = useState(null);

  const dishes = [
    {
      image: "/assets/dishes/risotto-ai-funghi.jpg",
      title: "Scallops e funghi risotto",
      desc: "Pan-seared scallops atop creamy risotto with wild mushrooms and a delicate truffle essence"
    },
    {
      image: "/assets/dishes/sea-bass-1.webp",
      title: "Chilean seabass",
      desc: "Miso-glazed Chilean seabass served with sautéed baby bok choy and ginger-soy reduction"
    },
    {
      image: "/assets/dishes/veal-osso-bucco-cropped.jpg",
      title: "Veal Osso Buco",
      desc: "Slow-braised veal shanks in a rich wine reduction, served with saffron risotto and gremolata"
    },
    {
      image: "/assets/dishes/<EMAIL>",
      title: "Ribeye 16oz with crackling",
      desc: "Prime-aged ribeye grilled to perfection, topped with crispy pork crackling and served with truffle mashed potatoes"
    },
    {
      image: "/assets/dishes/Spicy-Rigatoni_THUMB.jpg",
      title: "Spicy vodka rigatoni",
      desc: "Al dente rigatoni tossed in a velvety tomato cream sauce infused with vodka and Calabrian chilies"
    }
  ];

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % dishes.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [dishes.length]);

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? dishes.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => 
      (prevIndex + 1) % dishes.length
    );
  };

  const handleTouchStart = (e) => {
    const touchDown = e.touches[0].clientX;
    setTouchPosition(touchDown);
  };

  const handleTouchMove = (e) => {
    if (touchPosition === null) {
      return;
    }

    const currentTouch = e.touches[0].clientX;
    const diff = touchPosition - currentTouch;

    if (diff > 5) {
      handleNext();
    }

    if (diff < -5) {
      handlePrevious();
    }

    setTouchPosition(null);
  };

  const goToSlide = (slideIndex) => {
    setCurrentIndex(slideIndex);
  };

  return (
    <div className="app__bg app__wrapper section__padding" id="menu-highlights">
      <div className="app__wrapper_info">
        <SubHeading title="Menu highlights" />
        <h1 className="headtext__cormorant">Signature Dishes</h1>
        
        <div className="app__chef-content">
          <p className="p__opensans">
            Experience the pinnacle of culinary excellence with our exquisitely crafted signature dishes.
          </p>
          <div className="menu-button-container">
            <Link href="/menu">
              <button type="button" className="custom__button">
                View Full Menu
              </button>
            </Link>
          </div>
        </div>
      </div>

      <div 
        className="app__dishes-carousel"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
      >
        <div 
          className="app__dishes-slider"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {dishes.map((dish, index) => (
            <div className="dish-slide" key={`dish-${index + 1}`}>
              <div className="dish-item">
                <div className="dish-img-container">
                  <Image 
                    src={dish.image}
                    alt={dish.title}
                    fill
                    sizes="(max-width: 650px) 90vw, (max-width: 1200px) 50vw, 600px"
                    className="dish-img"
                    priority={index === 0}
                  />
                </div>
                <h3 className="dish-title">{dish.title}</h3>
                <p className="dish-desc">{dish.desc}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="app__dishes-arrows">
          <BsArrowLeftShort
            className="dishes__arrow-icon left"
            onClick={handlePrevious}
          />
          <BsArrowRightShort
            className="dishes__arrow-icon right"
            onClick={handleNext}
          />
        </div>

        <div className="app__dishes-dots">
          {dishes.map((_, index) => (
            <span 
              key={`dot-${index}`} 
              className={`dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
            ></span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Chef;
